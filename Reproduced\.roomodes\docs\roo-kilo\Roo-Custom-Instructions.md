---
description: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> comment utiliser les instructions personnalisées pour adapter le comportement de Roo Code à vos préférences, votre style de codage et les exigences de vos projets.
keywords: instructions personnalisées,personnalisation,personnalisation IA,préférences de codage,règles de projet
image: /img/social-share.jpg
---

# Instructions personnalisées

Les instructions personnalisées vous permettent de personnaliser le comportement de Roo, en fournissant des directives spécifiques qui influencent ses réponses, son style de codage et ses processus de prise de décision.

## Emplacement des fichiers d'instructions

Vous pouvez fournir des instructions personnalisées via des règles globales (appliquées à tous les projets), des règles d'espace de travail (spécifiques à un projet), ou via l'interface de l'onglet Prompts.

**Répertoire des règles globales** : S'applique automatiquement à tous les projets.

- Linux/macOS : `~/.roo/rules/` et `~/.roo/rules-{modeSlug}/`
- Windows : `%USERPROFILE%\.roo\rules\` et `%USERPROFILE%\.roo\rules-{modeSlug}\`

**Règles d'espace de travail** : S'appliquent uniquement au projet courant et peuvent remplacer les règles globales.

- Méthode recommandée : Dossier `.roo/rules/`
    ```
    .roo/
        └── rules/          # Règles pour tout l'espace de travail
                ├── 01-general.md
                └── 02-coding-style.txt
    ... (autres fichiers du projet)
    ```
- Méthode alternative : Fichier unique `.roorules`
    ```
    .roorules             # Règles pour tout l'espace de travail (fichier unique)
    ... (autres fichiers du projet)
    ```

**Instructions spécifiques à un mode** : S'appliquent uniquement à un mode particulier (ex : code).

- Méthode recommandée : Dossier `.roo/rules-{modeSlug}/`
    ```
    .roo/
        └── rules-code/     # Règles pour le mode "code"
                ├── 01-js-style.md
                └── 02-ts-style.md
    ... (autres fichiers du projet)
    ```
- Méthode alternative : Fichier unique `.roorules-{modeSlug}`
    ```
    .roorules-code        # Règles pour le mode "code" (fichier unique)
    ... (autres fichiers du projet)
    ```

Les règles sont chargées dans cet ordre : d'abord les règles globales, puis les règles d'espace de travail (qui peuvent les remplacer). Voir la section sur le répertoire global pour plus de détails.

## Que sont les instructions personnalisées ?

Les instructions personnalisées définissent des comportements, préférences et contraintes spécifiques, au-delà du rôle de base de Roo. Exemples : style de codage, normes de documentation, exigences de tests, directives de workflow.

## Définir des instructions personnalisées

### Instructions globales

Ces instructions s'appliquent à tous les espaces de travail et maintiennent vos préférences quel que soit le projet.

**Comment les définir :**

1. Ouvrez l'onglet Prompts : cliquez sur l'icône dans la barre supérieure de Roo Code
2. Trouvez la section : "Instructions personnalisées pour tous les modes"
3. Saisissez vos instructions dans la zone de texte
4. Cliquez sur "Terminé" pour enregistrer

### Répertoire des règles globales

Le répertoire des règles globales permet de définir des instructions réutilisables qui s'appliquent automatiquement à tous vos projets. Ce système prend en charge la configuration globale et les surcharges spécifiques à chaque projet.

#### Principaux avantages

**Sans règles globales** :
- Copier les mêmes règles dans chaque nouveau projet
- Mettre à jour manuellement les règles dans plusieurs projets
- Manque de cohérence entre les projets

**Avec règles globales** :
- Définir vos standards de codage une seule fois
- Surcharger certaines règles par projet si besoin
- Maintenir la cohérence sur tous vos projets
- Mise à jour facile pour tous les projets

#### Structure des dossiers

L'emplacement du répertoire global est fixe et ne peut pas être personnalisé :

- Linux/macOS : `~/.roo/rules/`
- Windows : `%USERPROFILE%\.roo\rules\`

#### Mise en place des règles globales

1. **Créer le répertoire global** :
     - Linux/macOS : `mkdir -p ~/.roo/rules`
     - Windows : `mkdir %USERPROFILE%\.roo\rules`
2. **Ajouter des règles générales** (`~/.roo/rules/coding-standards.md`) :
     ```
     # Standards de codage globaux
     1. Toujours utiliser TypeScript pour les nouveaux projets
     2. Écrire des tests unitaires pour toutes les nouvelles fonctions
     3. Utiliser des noms de variables explicites
     4. Ajouter des commentaires JSDoc pour les API publiques
     ```
3. **Ajouter des règles spécifiques à un mode** (`~/.roo/rules-code/typescript-rules.md`) :
     ```
     # Règles pour le mode TypeScript
     1. Utiliser le mode strict dans tsconfig.json
     2. Préférer les interfaces aux alias de type pour les objets
     3. Toujours spécifier le type de retour des fonctions
     ```

#### Répertoires de règles disponibles

| Dossier                        | Usage                                      |
|------------------------------- |--------------------------------------------|
| `rules/`                       | Règles générales pour tous les modes       |
| `rules-code/`                  | Règles spécifiques au mode Code            |
| `rules-docs-extractor/`        | Règles pour l'extraction de documentation  |
| `rules-architect/`             | Règles pour l'architecture système         |
| `rules-debug/`                 | Règles pour le débogage                    |
| `rules-{mode}/`                | Règles pour tout mode personnalisé         |

#### Ordre de chargement des règles

1. Règles globales (`~/.roo/`)
2. Règles de projet (`project/.roo/`) — peuvent remplacer les règles globales
3. Fichiers hérités (`.roorules`, `.clinerules` — compatibilité ascendante)

Dans chaque niveau, les règles spécifiques à un mode sont chargées avant les règles générales.

### Instructions au niveau de l'espace de travail

Ces instructions ne s'appliquent qu'à l'espace de travail courant, pour personnaliser Roo Code selon le projet.

#### Instructions globales via fichiers/dossiers

- **Méthode recommandée : dossier `.roo/rules/`**
    - Créez un dossier `.roo/rules/` à la racine du projet.
    - Placez-y vos fichiers d'instructions (`.md`, `.txt`, etc.). Roo Code les lit récursivement et les ajoute à l'invite système par ordre alphabétique.
- **Méthode alternative : fichier `.roorules`**
    - Si le dossier n'existe pas ou est vide, Roo Code cherche un fichier `.roorules` à la racine.

#### Instructions spécifiques à un mode

Deux méthodes indépendantes, utilisables ensemble :

1. **Via l'onglet Prompts**
     - Ouvrez l'onglet Prompts (icône dans la barre supérieure)
     - Sous "Modes", cliquez sur le bouton du mode à personnaliser
     - Saisissez vos instructions dans "Instructions personnalisées pour ce mode"
     - Cliquez sur "Terminé" pour enregistrer

     *Si le mode est global, les instructions s'appliquent à tous les espaces de travail pour ce mode.*

2. **Via fichiers/dossiers**
     - **Méthode recommandée : dossier `.roo/rules-{modeSlug}/`**
         - Créez un dossier `.roo/rules-{modeSlug}/` (ex : `.roo/rules-docs-writer/`) à la racine du projet.
         - Placez-y vos fichiers d'instructions (chargement récursif, ordre alphabétique).
     - **Méthode alternative : fichier `.roorules-{modeSlug}`**
         - Si le dossier n'existe pas ou est vide, Roo Code cherche un fichier `.roorules-{modeSlug}` (ex : `.roorules-code`) à la racine.

Les instructions de l'onglet Prompts, les règles globales, les règles d'espace de travail et les règles spécifiques à un mode sont toutes combinées. Voir la section suivante pour l'ordre exact.

## Comment les instructions sont combinées

Les instructions sont placées dans l'invite système dans ce format exact :

> **Note** : Les règles globales sont chargées en premier, puis les règles d'espace de travail qui peuvent les remplacer. Les règles spécifiques à un mode apparaissent avant les règles générales à chaque niveau, et les dossiers priment sur les fichiers uniques.

## Règles concernant les fichiers `.rules`

- **Emplacement** : méthode recommandée via dossiers dans `.roo/` (`.roo/rules/` et `.roo/rules-{modeSlug}/`). Méthode alternative via fichiers uniques (`.roorules` et `.roorules-{modeSlug}`) à la racine du projet.
- **Fichiers vides** : ignorés sans message d'erreur
- **En-têtes de source** : chaque fichier d'instructions est inclus avec un en-tête indiquant sa source
- **Interaction des règles** : les règles spécifiques à un mode complètent les règles globales, elles ne les remplacent pas

## Exemples d'instructions personnalisées

- "Toujours utiliser des espaces pour l'indentation, largeur de 4 espaces"
- "Utiliser camelCase pour les noms de variables"
- "Écrire des tests unitaires pour toutes les nouvelles fonctions"
- "Expliquer votre raisonnement avant de fournir du code"
- "Favoriser la lisibilité et la maintenabilité du code"
- "Privilégier la bibliothèque la plus courante dans la communauté"
- "Pour toute nouvelle fonctionnalité web, garantir la réactivité et l'accessibilité"

**Astuce pour les équipes** :

- **Standards de projet** : utilisez les dossiers `.roo/rules/` sous contrôle de version pour standardiser le comportement de Roo sur un projet. Cela garantit la cohérence du style et des workflows entre membres de l'équipe.
- **Standards d'organisation** : utilisez les règles globales (`~/.roo/rules/`) pour définir des standards à l'échelle de l'organisation. Les membres de l'équipe peuvent partager les mêmes règles globales pour une cohérence totale.
- **Approche hybride** : combinez des règles globales pour l'organisation et des règles d'espace de travail pour les besoins spécifiques du projet. Les règles d'espace de travail peuvent remplacer les règles globales si nécessaire.

L'approche basée sur les dossiers offre une meilleure organisation que les fichiers `.roorules` uniques et prend en charge la personnalisation globale et par projet.

## Combinaison avec des modes personnalisés

Pour une personnalisation avancée, combinez avec des modes personnalisés pour créer des environnements spécialisés avec des outils spécifiques, des restrictions de fichiers et des instructions adaptées.
