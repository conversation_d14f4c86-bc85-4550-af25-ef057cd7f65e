J'ai créé le plan suivant après une exploration et une analyse approfondies de la base de code. Suivez ce plan à la lettre. Faites confiance aux fichiers et références indiqués. Ne revérifiez pas ce qui est écrit dans le plan. Explorez uniquement si cela s'avère absolument nécessaire. Commencez par implémenter tous les changements de fichiers proposés, puis je relirai l'ensemble des modifications à la fin.


### Observations

Après analyse du système de modes personnalisés de RooCode et KiloCode, il apparaît que les deux outils partagent un schéma YAML identique pour la configuration des modes. Le système actuel prend déjà en charge toutes les fonctionnalités nécessaires à la mise en œuvre des workflows SDD/EARS et des modes méthodologiques. Les fichiers `.roomodes` et `.kilocodemodes` contiennent trois modes illustrant l’orchestration, la recherche web et l’extraction de données. La documentation décrit un système robuste de gestion des permissions, de restrictions de fichiers, de chargement de contexte et d’intégration avec les listes de tâches et les protocoles MCP.

### Approche

Je vais créer un ensemble complet de modes personnalisés organisés en trois catégories : les modes SDD fondamentaux (implémentant le Spec-Driven Development à la Kiro avec la syntaxe EARS), les modes méthodologiques (incarnant des approches de développement logiciel spécifiques) et les modes persona de rôle (simulant les perspectives des membres de l’équipe). L’implémentation s’appuiera sur le schéma YAML existant, le système de permissions et les mécanismes de gestion du contexte. Chaque mode sera conçu avec des permissions adaptées et des frontières claires pour garantir une collaboration harmonieuse tout en respectant le principe de responsabilité unique.

### Raisonnement

J’ai analysé les fichiers de configuration des modes personnalisés pour comprendre la structure du schéma YAML utilisée par RooCode et KiloCode. J’ai étudié le système de permissions pour comprendre comment accorder

## Mermaid Diagram

```mermaid
sequenceDiagram
  participant Utilisateur
  participant SDD_Req as Rédacteur des exigences SDD
  participant SDD_Design as Architecte de conception SDD
  participant SDD_Task as Orchestrateur des tâches SDD
  participant Method as Mode Méthodologie
  participant Role as Persona de rôle
  participant TodoList as Système de liste de tâches

  Utilisateur->>SDD_Req: Demande une nouvelle fonctionnalité
  SDD_Req->>SDD_Req: Génère requirements.md avec la syntaxe EARS
  SDD_Req->>Utilisateur: Présente les exigences pour validation
  
  Utilisateur->>SDD_Design: Approuve les exigences
  SDD_Design->>SDD_Design: Analyse la base de code + exigences
  SDD_Design->>SDD_Design: Génère design.md
  SDD_Design->>Utilisateur: Présente la conception pour validation
  
  Utilisateur->>SDD_Task: Approuve la conception
  SDD_Task->>SDD_Task: Décompose en tasks.md
  SDD_Task->>TodoList: Crée la liste de tâches
  SDD_Task->>Utilisateur: Présente le plan de tâches
  
  Utilisateur->>Method: Sélectionne le mode méthodologique
  Method->>SDD_Task: Applique les pratiques spécifiques à la méthodologie
  Method->>TodoList: Met à jour avec le workflow méthodologique
  
  Utilisateur->>Role: Consulte la persona de rôle
  Role->>Utilisateur: Fournit des conseils spécifiques au domaine
  Role->>SDD_Task: Influence la priorisation des tâches
  
  SDD_Task->>TodoList: Suit la progression
  TodoList->>Utilisateur: Rapporte l’état d’avancement
```

## Modifications de fichiers proposées

### tools(NOUVEAU)

Créer un répertoire `tools` pour héberger les scripts d'automatisation liés à la gestion des modes personnalisés. Ce répertoire contiendra des utilitaires pour synchroniser les définitions de modes entre RooCode et KiloCode, valider la syntaxe YAML et garantir la cohérence de l'écosystème des modes.

### tools\mode-sync.py(NOUVEAU)

Références : 

- .roomodes(MODIFIER)
- .kilocodemodes(MODIFIER)

Créer un script Python qui synchronise les définitions de modes personnalisés entre les fichiers `.roomodes` et `.kilocodemodes`. Le script doit :

- Lire les définitions de modes à partir d'une source de vérité unique
- Valider la syntaxe YAML et la présence des champs obligatoires (slug, name, roleDefinition, customInstructions, groups, source)
- Vérifier l'unicité des slugs pour éviter les conflits
- Mettre à jour les fichiers `.roomodes` et `.kilocodemodes` avec un contenu identique
- Fournir des logs et des rapports d'erreur en cas d'échec de validation
- Prendre en charge la mise à jour individuelle ou en masse des modes

Le script doit utiliser PyYAML pour le parsing et maintenir la structure et le formatage exacts des fichiers existants. Inclure des arguments en ligne de commande pour différentes opérations (sync, validate, add-mode).

### templates\modes(NOUVEAU)

Créer un répertoire `templates\modes` pour héberger les fichiers modèles de définitions de modes. Ceux-ci fourniront la structure canonique pour la création de nouveaux modes personnalisés, garantissant cohérence et exhaustivité.

### templates\modes\mode-template.yaml(NOUVEAU)

Références : 

- .roomodes(MODIFIER)
- docs\roo-kilo\Roo-Customizing-Modes.md

Créer un modèle YAML canonique pour les modes personnalisés incluant tous les champs obligatoires et optionnels avec des commentaires de documentation. Le modèle doit inclure :

- Structure complète des champs avec annotations de type en commentaires
- Exemples pour chaque type de champ
- Conseils sur les groupes de permissions (read, edit, browser, command, mcp)
- Exemples de patterns regex pour les restrictions d’édition
- Bonnes pratiques pour le formatage de customInstructions
- Références à la documentation du schéma existant

Ce modèle servira de point de départ pour toute création de nouveau mode et assurera la cohérence de l’écosystème.

### modes\sdd-foundation(NOUVEAU)

Créer un répertoire pour les modes fondamentaux SDD (Spec-Driven Development) qui implémentent le workflow Kiro de base. Ces modes serviront de socle pour tous les autres modes méthodologiques et basés sur les rôles.

### modes\sdd-foundation\sdd-requirements-writer.yaml(NOUVEAU)

Références : 

- docs\SDD\Rapport-SDD-EARS.md
- docs\kiro\kiro.md

Créer une définition de mode pour le SDD Requirements Writer spécialisé dans la génération de fichiers requirements.md en syntaxe EARS. Le mode doit :

- Avoir le slug : `sdd-requirements-writer`
- Nom : `📋 SDD Requirements Writer`
- roleDefinition : Se concentrer sur l’élaboration et la documentation des exigences en utilisant les patterns EARS
- whenToUse : Lors du démarrage d’une nouvelle fonctionnalité ou pour formaliser des exigences
- groups : `[read, mcp]` (mode conseil, sans permissions d’édition)
- customInstructions : Inclure des instructions détaillées sur :
  - Les cinq patterns EARS (Ubiquitous, Event-Driven, State-Driven, Unwanted Behavior, Optional Feature)
  - Des exemples pour chaque pattern avec la bonne syntaxe
  - Comment décomposer les user stories en exigences EARS
  - L’intégration avec le workflow SDD en trois phases
  - Références à `docs/SDD/Rapport-SDD-EARS.md` pour la méthodologie détaillée
  - Modèles pour la structure de requirements.md
  - Critères de validation pour la qualité des exigences

Le mode doit guider l’utilisateur dans la collecte systématique des exigences tout en imposant la conformité à la syntaxe EARS.

### modes\sdd-foundation\sdd-design-architect.yaml(NOUVEAU)

Références : 

- docs\kiro\kiro.md
- templates\features\exemple\design.md

Créer une définition de mode pour le SDD Design Architect qui génère des fichiers design.md à partir de l'analyse des exigences et de la base de code existante. Le mode doit :

- Avoir le slug : `sdd-design-architect`
- Nom : `🏗️ SDD Design Architect`
- roleDefinition : Analyse les exigences et le code existant pour produire des documents de conception technique
- whenToUse : Après la finalisation des exigences et avant le début de l’implémentation
- groups : `[read, mcp]` (mode conseil, accès en lecture à la base de code, sans édition)
- customInstructions : Inclure des instructions sur :
  - Lecture et analyse des fichiers requirements.md utilisant la syntaxe EARS
  - Examen de la structure et des patterns du code existant
  - Création de diagrammes architecturaux avec la syntaxe Mermaid
  - Définition des interfaces, modèles de données et points d’API
  - Patterns d’intégration avec les systèmes existants
  - Gestion des erreurs et des cas limites
  - Considérations de performance et de scalabilité
  - Références à `docs/kiro/kiro.md` pour la méthodologie de la phase de conception
  - Modèles pour la structure de design.md incluant vue d’architecture, responsabilités des composants, diagrammes de flux de données

Le mode doit faire le lien entre les exigences métier et l’implémentation technique.

### modes\sdd-foundation\sdd-task-orchestrator.yaml(NOUVEAU)

Références : 

- .roomodes(MODIFIER)
- docs\roo-kilo\Roo-Todo-List.md
- templates\features\exemple\tasks.md

Créer une définition de mode pour le SDD Task Orchestrator qui décompose la conception en tâches actionnables et gère le workflow des tâches. Le mode doit :

- Avoir le slug : `sdd-task-orchestrator`
- Nom : `🎯 SDD Task Orchestrator`
- roleDefinition : Convertit les documents de conception en plans d’implémentation granulaires et gère l’exécution des tâches
- whenToUse : Après validation de la conception pour créer la feuille de route d’implémentation et pendant le développement pour suivre l’avancement
- groups : `[read, edit, mcp]` avec fileRegex : `'\.(md|json)$'` (peut éditer la documentation et les fichiers de configuration)
- customInstructions : Inclure des instructions sur :
  - Lecture des fichiers requirements.md et design.md pour comprendre le périmètre
  - Décomposition des fonctionnalités en tâches atomiques et testables
  - Création de graphes de dépendances entre les tâches
  - Intégration avec l’outil `update_todo_list` pour le suivi de l’avancement
  - Génération de tasks.md avec liens vers exigences et conception
  - Estimation et priorisation des tâches
  - Coordination avec les autres modes pour l’exécution des tâches
  - Références au mode `advanced-orchestrator` existant pour les patterns de délégation
  - Modèles pour la structure de tasks.md avec critères d’acceptation clairs
  - Capacités de suivi et de reporting de l’avancement

Le mode doit agir comme coordinateur central du workflow SDD, assurant la transition du planning à l’exécution.

### modes\methodologies(NOUVEAU)

Créer un répertoire pour les modes spécifiques aux méthodologies qui implémentent diverses approches de développement logiciel. Chaque mode méthodologique s’intègre avec la fondation SDD tout en ajoutant des pratiques et workflows propres à la méthodologie.

### modes\methodologies\agile-coach.yaml(NOUVEAU)

Créer une définition de mode pour un Agile Coach qui fournit des conseils sur la méthodologie agile. Le mode doit :

- Avoir le slug : `agile-coach`
- Nom : `🔄 Agile Coach`
- roleDefinition : Guide les équipes dans les principes agiles, les pratiques et l’amélioration continue
- whenToUse : Pour l’accompagnement agile général, les rétrospectives et l’amélioration des processus
- groups : `[read, mcp]` (rôle conseil)
- customInstructions : Inclure des instructions sur :
  - Principes et valeurs du manifeste agile
  - Développement itératif et boucles de feedback
  - Rédaction de user stories et gestion du backlog
  - Planification de sprint et techniques d’estimation
  - Animation de rétrospectives
  - Pratiques d’amélioration continue
  - Intégration avec le workflow SDD pour un développement structuré
  - Coaching des équipes sur l’adoption agile
  - Identification et suppression des obstacles
  - Mesure et suivi des métriques agiles

Le mode doit fournir des conseils agiles de haut niveau tout en encourageant l’utilisation des modes SDD pour le travail détaillé.

### modes\methodologies\scrum-master.yaml(NOUVEAU)

Références : 

- docs\roo-kilo\Roo-Todo-List.md

Créer une définition de mode pour un Scrum Master qui facilite les cérémonies et processus Scrum. Le mode doit :

- Avoir le slug : `scrum-master`
- Nom : `🏃 Scrum Master`
- roleDefinition : Facilite les cérémonies Scrum, supprime les obstacles et veille au respect du processus Scrum
- whenToUse : Pour la planification de sprint, les standups quotidiens, les revues de sprint et les rétrospectives
- groups : `[read, edit, mcp]` avec fileRegex : `'(sprint|backlog|retrospective).*\.(md|json)$'` (peut éditer les documents liés aux sprints)
- customInstructions : Inclure des instructions sur :
  - Cadre Scrum et cérémonies
  - Planification de sprint et définition des objectifs
  - Animation des standups quotidiens
  - Préparation des revues de sprint et des démos
  - Techniques de rétrospective et suivi des actions
  - Raffinement du backlog et estimation des stories
  - Identification et suppression des obstacles
  - Suivi de la vélocité et prévisions d’équipe
  - Intégration avec la todo list pour le suivi des sprints
  - Coordination avec les modes SDD pour le développement des fonctionnalités
  - Suivi des métriques Scrum et reporting

Le mode doit faire respecter les pratiques Scrum tout en s’appuyant sur le SDD pour le développement structuré des fonctionnalités.

### modes\methodologies\kanban-manager.yaml(NOUVEAU)

Créer une définition de mode pour un Kanban Manager qui implémente les principes du workflow Kanban. Le mode doit :

- Avoir le slug : `kanban-manager`
- Nom : `📊 Kanban Manager`
- roleDefinition : Gère les tableaux Kanban, les limites WIP et l’optimisation du flux continu
- whenToUse : Pour les workflows de livraison continue et le développement basé sur le flux
- groups : `[read, edit, mcp]` avec fileRegex : `'(kanban|board|wip).*\.(md|json)$'` (peut éditer les documents liés à Kanban)
- customInstructions : Inclure des conseils sur :
  - Principes et pratiques Kanban
  - Gestion des limites de travail en cours (WIP)
  - Optimisation des métriques de flux et du temps de cycle
  - Livraison continue et déploiement
  - Gestion visuelle du workflow
  - Identification et résolution des goulots d’étranglement
  - Accords de niveau de service et prévisibilité
  - Intégration avec SDD pour la décomposition des fonctionnalités
  - Attribution du travail en mode pull
  - Diagrammes de flux cumulatifs et analyses

Le mode doit optimiser le flux continu tout en maintenant les standards de qualité SDD.

### modes\methodologies\fdd-coordinator.yaml(NOUVEAU)

Créer une définition de mode pour un Coordinateur Feature-Driven Development (FDD). Le mode doit :

- Avoir le slug : `fdd-coordinator`
- Nom : `🎯 FDD Coordinator`
- roleDefinition : Coordonne le développement piloté par les fonctionnalités avec modélisation du domaine et ensembles de fonctionnalités
- whenToUse : Pour les projets à grande échelle nécessitant une modélisation du domaine et un développement centré sur les fonctionnalités
- groups : `[read, edit, mcp]` avec fileRegex : `'(feature|domain|model).*\.(md|json)$'` (peut éditer les documents de fonctionnalités et de domaine)
- customInstructions : Inclure des conseils sur :
  - Modélisation du domaine et analyse orientée objet
  - Développement et catégorisation de la liste des fonctionnalités
  - Planification et ordonnancement des ensembles de fonctionnalités
  - Attribution des classes et équipes de programmeurs en chef
  - Processus de conception et de construction par fonctionnalité
  - Intégration avec SDD pour la spécification détaillée des fonctionnalités
  - Suivi de l’avancement par complétion des fonctionnalités
  - Identification et utilisation de l’expertise métier
  - Gestion des dépendances entre fonctionnalités
  - Assurance qualité au niveau des fonctionnalités

Le mode doit coordonner le développement à grande échelle tout en s’appuyant sur SDD pour l’implémentation de chaque fonctionnalité.

### modes\methodologies\tdd-guide.yaml(NOUVEAU)

Références : 

- docs\SDD\Rapport-SDD-EARS.md

Créer une définition de mode pour un Guide Test-Driven Development (TDD). Le mode doit :

- Avoir le slug : `tdd-guide`
- Nom : `🧪 TDD Guide`
- roleDefinition : Guide les pratiques TDD avec les cycles red-green-refactor
- whenToUse : Pour mettre en œuvre les pratiques TDD et assurer une couverture de tests complète
- groups : `[read, edit, mcp]` avec fileRegex : `'(test|spec).*\.(js|ts|py|java|cs|rb)$'` (peut éditer les fichiers de tests)
- customInstructions : Inclure des conseils sur :
  - Mise en œuvre du cycle Red-Green-Refactor
  - Écriture des tests en échec en premier
  - Production du code minimal pour faire passer les tests
  - Refactoring en toute confiance
  - Patterns de conception de tests et bonnes pratiques
  - Intégration avec les exigences SDD pour les scénarios de test
  - Traduction des exigences EARS en cas de test
  - Couverture de tests et métriques de qualité
  - Stratégies de tests unitaires, d’intégration et d’acceptation
  - Techniques de mock et d’isolation des tests
  - Tests continus et boucles de feedback

Le mode doit imposer la discipline TDD tout en intégrant les exigences EARS pour une couverture de test exhaustive.

### modes\methodologies\bdd-facilitator.yaml(NOUVEAU)

Références : 

- docs\SDD\Rapport-SDD-EARS.md

Créer une définition de mode pour un Facilitateur Behavior-Driven Development (BDD). Le mode doit :

- Avoir le slug : `bdd-facilitator`
- Nom : `🎭 BDD Facilitator`
- roleDefinition : Facilite le BDD avec des scénarios Given-When-Then
- whenToUse : Pour la spécification collaborative et la définition des critères d’acceptation
- groups : `[read, edit, mcp]` avec fileRegex : `'(feature|scenario|step).*\.(feature|md|js|ts|py)$'` (peut éditer les fichiers liés au BDD)
- customInstructions : Inclure des conseils sur :
  - Rédaction de scénarios Given-When-Then
  - Ateliers de spécification collaborative
  - Documentation vivante avec spécifications exécutables
  - Définition et validation des critères d’acceptation
  - Intégration avec la syntaxe EARS pour la spécification du comportement
  - Syntaxe Cucumber/Gherkin et bonnes pratiques
  - Example mapping et découverte de scénarios
  - Collaboration et communication avec les parties prenantes
  - Tests d’acceptation automatisés
  - Raffinement des spécifications comportementales

Le mode doit faire le lien entre exigences métier et implémentation technique via la spécification collaborative du comportement.

### modes\roles(NOUVEAU)

Créer un répertoire pour les modes persona basés sur les rôles qui simulent les perspectives des membres de l’équipe. Ces modes fourniront une expertise et un point de vue métier tout en restant orthogonaux aux choix méthodologiques.

### modes\roles\product-owner.yaml(NOUVEAU)

Références : 

- docs\SDD\Rapport-SDD-EARS.md

Créer une définition de mode pour une persona Product Owner. Le mode doit :

- Avoir le slug : `role-product-owner`
- Nom : `💼 Product Owner`
- roleDefinition : Représente la perspective valeur métier, priorise les fonctionnalités et définit les critères d’acceptation
- whenToUse : Pour la clarification des besoins métier, la priorisation des fonctionnalités et la définition des critères d’acceptation
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Évaluation de la valeur métier et analyse du ROI
  - Rédaction de user stories et critères d’acceptation
  - Priorisation des fonctionnalités et gestion du backlog
  - Communication avec les parties prenantes et recueil des besoins
  - Analyse de marché et positionnement concurrentiel
  - Mapping de l’expérience utilisateur et du parcours client
  - Intégration avec la syntaxe EARS pour des exigences claires
  - Planification de la roadmap produit et communication de la vision
  - Définition des métriques et critères de succès
  - Évaluation et gestion des risques

Le mode doit se concentrer sur la valeur métier et les besoins utilisateurs tout en soutenant les équipes techniques avec des exigences claires.

### modes\roles\scrum-master-role.yaml(NOUVEAU)

Créer une définition de mode pour une persona Scrum Master (distincte du mode méthodologique). Le mode doit :

- Avoir le slug : `role-scrum-master`
- Nom : `🏃‍♂️ Scrum Master (Role)`
- roleDefinition : Se concentre sur la dynamique d’équipe, l’amélioration des processus et la suppression des obstacles dans une posture de servant leader
- whenToUse : Pour le coaching d’équipe, le conseil sur les processus et l’identification des obstacles organisationnels
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Leadership de service et autonomisation de l’équipe
  - Techniques de facilitation et gestion des réunions
  - Résolution de conflits et dynamique d’équipe
  - Identification des obstacles organisationnels
  - Gestion du changement et transformation agile
  - Approches de coaching et de mentorat
  - Suivi de la performance et de la santé de l’équipe
  - Gestion des parties prenantes et communication
  - Amélioration continue et animation des rétrospectives
  - Évaluation de la maturité agile et plan de progression

Le mode doit se concentrer sur l’accompagnement de l’équipe et l’optimisation des processus plutôt que sur l’exécution directe.

### modes\roles\developer.yaml(NOUVEAU)

Références : 

- docs\kiro\kiro.md

Créer une définition de mode pour une persona Développeur. Le mode doit :

- Avoir le slug : `role-developer`
- Nom : `👨‍💻 Developer`
- roleDefinition : Apporte la perspective d’implémentation technique, l’accent sur la qualité du code et les bonnes pratiques d’ingénierie
- whenToUse : Pour l’évaluation de la faisabilité technique, la planification de l’implémentation et le conseil sur la qualité du code
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Analyse de faisabilité technique et estimation
  - Standards de qualité du code et bonnes pratiques
  - Patterns d’architecture et principes de conception
  - Optimisation des performances et considérations de scalabilité
  - Bonnes pratiques de sécurité et évaluation des vulnérabilités
  - Lignes directrices pour la revue de code et feedback
  - Identification et gestion de la dette technique
  - Intégration avec SDD pour la planification de l’implémentation
  - Stratégies de tests et assurance qualité
  - Documentation et partage de connaissances

Le mode doit fournir une expertise technique tout en soutenant un développement structuré via les pratiques SDD.

### modes\roles\tester.yaml(NOUVEAU)

Références : 

- docs\SDD\Rapport-SDD-EARS.md

Créer une définition de mode pour une persona Testeur. Le mode doit :

- Avoir le slug : `role-tester`
- Nom : `🔍 Testeur`
- roleDefinition : Se concentre sur l'assurance qualité, la stratégie de test et la prévention des défauts du point de vue QA
- whenToUse : Pour la planification des tests, la stratégie qualité et l'analyse des défauts
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Développement et planification de la stratégie de test
  - Conception des cas de test et analyse de la couverture
  - Identification des défauts et analyse des causes racines
  - Métriques qualité et reporting
  - Approches de test basées sur les risques
  - Stratégie d'automatisation et choix des outils
  - Considérations pour les tests de performance et de sécurité
  - Intégration avec les exigences EARS pour les scénarios de test
  - Tests d'acceptation utilisateur et validation
  - Tests continus et seuils de qualité

Le mode doit garantir une couverture qualité complète tout en s'intégrant aux exigences et conceptions SDD.

### modes\roles\architect.yaml(NOUVEAU)

Références : 

- docs\kiro\kiro.md

Créer une définition de mode pour une persona Architecte. Le mode doit :

- Avoir le slug : `role-architect`
- Nom : `🏛️ Architecte`
- roleDefinition : Apporte une perspective d'architecture système, des patterns de conception et une vision technique à long terme
- whenToUse : Pour les décisions architecturales, la conception système et la stratégie technique
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Conception d'architecture système et patterns
  - Sélection et évaluation de la stack technologique
  - Architecture de performance et scalabilité
  - Architecture de sécurité et conformité
  - Patterns d'intégration et conception d'API
  - Architecture et modélisation des données
  - Stratégies cloud et déploiement
  - Gestion de la dette technique et refactoring
  - Documentation et communication architecturale
  - Vision technique à long terme et feuille de route

Le mode doit fournir des conseils architecturaux de haut niveau tout en soutenant la conception détaillée via les processus SDD.

### modes\roles\analyst.yaml(NOUVEAU)

Références : 

- docs\SDD\Rapport-SDD-EARS.md

Créer une définition de mode pour une persona Analyste Fonctionnel. Le mode doit :

- Avoir le slug : `role-analyst`
- Nom : `📊 Analyste Fonctionnel`
- roleDefinition : Spécialiste de l'analyse des exigences, de la modélisation des processus métier et de la communication avec les parties prenantes
- whenToUse : Pour la collecte des besoins, l'analyse métier et la documentation des processus
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Techniques d'élucidation et d'analyse des exigences
  - Modélisation et optimisation des processus métier
  - Identification et gestion des parties prenantes
  - Analyse des écarts et évaluation des impacts
  - Développement de cas d'utilisation et scénarios
  - Analyse et documentation des flux de données
  - Intégration avec la syntaxe EARS pour des exigences précises
  - Développement de matrices de traçabilité
  - Analyse et gestion des impacts de changement
  - Définition et validation des règles métier

Le mode doit faire le lien entre besoins métier et solutions techniques par une analyse systématique et une documentation claire.

### modes\roles\ux-designer.yaml(NOUVEAU)

Créer une définition de mode pour une persona UX/UI Designer. Le mode doit :

- Avoir le slug : `role-ux-designer`
- Nom : `🎨 UX/UI Designer`
- roleDefinition : Se concentre sur l'expérience utilisateur, la conception d'interface et l'utilisabilité sous l'angle design
- whenToUse : Pour la planification de l'expérience utilisateur, la conception d'interface et les considérations d'utilisabilité
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Recherche utilisateur et développement de personas
  - Cartographie du parcours utilisateur et design d'expérience
  - Principes et patterns de conception d'interface
  - Tests d'utilisabilité et validation
  - Normes d'accessibilité et conformité
  - Développement et maintenance de design system
  - Techniques de prototypage et wireframing
  - Design visuel et cohérence de la marque
  - Considérations mobile et responsive
  - Collaboration et transmission avec l'équipe de développement

Le mode doit garantir une conception centrée utilisateur tout en s'intégrant aux exigences et contraintes techniques.

### modes\roles\devops.yaml(NOUVEAU)

Créer une définition de mode pour une persona Ingénieur DevOps. Le mode doit :

- Avoir le slug : `role-devops`
- Nom : `⚙️ Ingénieur DevOps`
- roleDefinition : Apporte une perspective infrastructure, déploiement et exploitation avec un accent sur l'automatisation et la fiabilité
- whenToUse : Pour la planification des déploiements, la conception d'infrastructure et les considérations opérationnelles
- groups : `[read]` (persona conseil, accès en lecture seule)
- customInstructions : Inclure des conseils sur :
  - Conception et optimisation de pipelines CI/CD
  - Infrastructure as code et automatisation
  - Stratégies de monitoring et observabilité
  - Sécurité et conformité lors du déploiement
  - Optimisation de la performance et de la scalabilité
  - Stratégies de sauvegarde et reprise après sinistre
  - Orchestration de conteneurs et microservices
  - Optimisation cloud et gestion des coûts
  - Gestion des releases et stratégies de déploiement
  - Excellence opérationnelle et gestion des incidents

Le mode doit garantir la préparation opérationnelle tout en soutenant les workflows de développement et les standards qualité.

### .roomodes(MODIFY)

Références : 

- modes\sdd-foundation\sdd-requirements-writer.yaml(NOUVEAU)
- modes\sdd-foundation\sdd-design-architect.yaml(NOUVEAU)
- modes\sdd-foundation\sdd-task-orchestrator.yaml(NOUVEAU)

Mettez à jour le fichier principal de configuration des modes personnalisés RooCode pour inclure tous les nouveaux modes SDD foundation, méthodologies et personas de rôle. Les nouveaux modes doivent être ajoutés au tableau `customModes` après les trois modes existants (advanced-orchestrator, fire-crawler, deep-research).

Ajoutez les modes suivants dans cet ordre :

**Modes SDD Foundation :**
- sdd-requirements-writer
- sdd-design-architect  
- sdd-task-orchestrator

**Modes Méthodologies :**
- agile-coach
- scrum-master
- kanban-manager
- fdd-coordinator
- tdd-guide
- bdd-facilitator

**Modes Personas de Rôle :**
- role-product-owner
- role-scrum-master
- role-developer
- role-tester
- role-architect
- role-analyst
- role-ux-designer
- role-devops

Chaque mode doit être ajouté avec la structure YAML complète incluant tous les champs requis (slug, name, roleDefinition, whenToUse, description, customInstructions, groups, source). Le contenu doit correspondre aux fichiers YAML individuels créés dans les répertoires de modes, mais consolidé dans le format du fichier de configuration principal.

Maintenez le style de formatage YAML existant et assurez la cohérence de l'indentation et de la structure avec les modes déjà présents.


### .kilocodemodes(MODIFIER)

Références : 

- .roomodes(MODIFIER)

Mettez à jour le fichier de configuration des modes personnalisés KiloCode pour qu’il corresponde exactement au fichier `.roomodes` mis à jour. Cela garantit une synchronisation complète entre les configurations RooCode et KiloCode.

Le fichier doit contenir un contenu identique à celui du fichier `.roomodes` mis à jour, incluant :
- Tous les modes existants (advanced-orchestrator, fire-crawler, deep-research)
- Tous les nouveaux modes SDD foundation
- Tous les nouveaux modes méthodologies  
- Tous les nouveaux modes persona de rôle

Maintenez une structure YAML, un formatage et un contenu strictement identiques afin que les deux IDE disposent des mêmes modes personnalisés. Ce fichier doit être maintenu synchronisé avec `.roomodes` via le script de synchronisation.

### docs\modes(NOUVEAU)

Créez un répertoire de documentation pour l’écosystème des modes personnalisés. Ce dossier regroupera la documentation complète pour chaque catégorie de mode, les guides d’utilisation et des exemples d’intégration.

### docs\modes\README.md(NOUVEAU)

Références : 

- docs\roo-kilo\Roo-Customizing-Modes.md

Rédigez un document d’introduction complet à l’écosystème des modes personnalisés. Le document doit inclure :

- Présentation des trois catégories de modes (SDD Foundation, Méthodologies, Personas de Rôle)
- Guide de démarrage rapide pour l’utilisation des modes
- Modèles d’intégration entre les différents types de modes
- Exemples de workflows courants combinant plusieurs modes
- Références vers la documentation détaillée de chaque catégorie
- Section de dépannage et FAQ
- Liens vers la galerie communautaire et les consignes de contribution

Ce document doit servir de point d’entrée pour les utilisateurs souhaitant comprendre et exploiter efficacement le système de modes personnalisés.

### docs\modes\sdd-foundation.md(OUVEAU)

Références : 

- docs\kiro\kiro.md
- docs\SDD\Rapport-SDD-EARS.md

Créez une documentation détaillée pour les modes SDD Foundation. Le document doit inclure :

- Présentation du workflow Spec-Driven Development (SDD)
- Explication détaillée de chaque mode fondation (requirements writer, design architect, task orchestrator)
- Référence de la syntaxe EARS avec exemples
- Intégration avec le processus SDD en trois phases
- Exemples d’utilisation et bonnes pratiques
- Intégration avec les todo lists et les systèmes MCP
- Dépannage des problèmes courants
- Références vers la documentation Kiro et le rapport SDD

Le document doit permettre aux utilisateurs de mettre en œuvre efficacement les workflows SDD à l’aide des modes fondation.

### docs\modes\methodologies.md(NOUVEAU)

Créez une documentation détaillée pour les modes méthodologiques. Le document doit inclure :

- Présentation de chaque méthodologie (Agile, Scrum, Kanban, FDD, TDD, BDD)
- Capacités spécifiques de chaque mode et cas d’usage
- Schémas d’intégration avec les modes SDD Foundation
- Workflows propres à chaque méthodologie et bonnes pratiques
- Exemples de combinaison des méthodologies avec les personas de rôle
- Conseils de configuration pour les besoins spécifiques d’équipe
- Matrice comparative des approches méthodologiques
- Conseils pour migrer d’une méthodologie à une autre

Le document doit aider les équipes à choisir et à mettre en œuvre les méthodologies adaptées à leur contexte.


### docs\modes\role-personas.md(NOUVEAU)

Créez une documentation détaillée pour les modes de rôles et de personnalités. Ce document doit inclure :

- Présentation de chaque persona de rôle et de ses domaines d’expertise
- Quand et comment solliciter les différents points de vue métiers
- Intégration avec les modes méthodologiques et fondation SDD
- Exemples de scénarios de collaboration multi-personas
- Conseils pour adapter et personnaliser les rôles selon l’organisation
- Bonnes pratiques pour des workflows de développement pilotés par les rôles
- Modèles de communication entre les différents rôles
- Gestion des désaccords et résolution des conflits entre perspectives métiers

Ce document doit permettre une utilisation efficace des perspectives métiers dans les workflows de développement.

### docs\modes\integration-examples.md(NOUVEAU)

References: 

- docs\roo-kilo\Roo-Todo-List.md
- docs\roo-kilo\Roo-Custom-Instructions.md

Create a comprehensive guide with practical examples of how to combine different modes for complete development workflows. The document should include:

- Exemples de workflows de bout en bout combinant modes SDD, méthodologies et personas de rôle
- Modèles d’utilisation par scénario (ex. : développement d’une nouvelle fonctionnalité, correction de bug, refactoring)
- Conseils pour la transition entre modes et procédures de passage de relais
- Schémas de collaboration en équipe utilisant plusieurs modes
- Intégration avec les outils et processus existants
- Astuces pour optimiser la performance et l’efficacité
- Pièges courants et comment les éviter
- Modèles avancés d’utilisation pour équipes expérimentées

Le document devrait fournir des conseils pratiques pour la mise en œuvre de flux de travail de développement complets assistés par l’IA.
