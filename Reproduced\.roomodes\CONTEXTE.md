> TEST

EFFECTUER UN TEST DE PLANIFICATION AVEC LES OUTILS SUIVANTS :

- TRAYCER
- AUGMENT (TASKLIST)
- GITHUB COPILOT
- GITHUB COPILOT + ZEN-MCP-SERVER (PLANNER)
- KILOCODE
- TRAE
- KIRO
- TASK-MASTER

---

CONDITIONS DE TEST :

PLUSIEURS NIVEAUX DE TESTS SONT NÉCESSAIRES POUR ÉVALUER LA PERFORMANCE DES OUTILS
LA MÊME REQUÊTE SERA UTILISÉE POUR TOUS LES OUTILS
DES FICHIERS NON INDIQUÉS DANS LA REQUÊTE SONT PRÉSENTS DANS LA BASE DE CODE (TEMPLATE SDD + Custom Modes RooCode/KiloCode)

---

TEST 1 :

---

- <PERSON><PERSON><PERSON><PERSON> @.kilocodemodes et @.roomodes pour identifier la structure des modes personnalisés de ces deux outils similaires.

- Lire les documents dans le dossier @docs\roo-kilo\* pour comprendre le certaines fonctionnalités de ces outils qui nous intéressent.

- Lire @docs\kiro\kiro.md pour comprendre les fonctionnalités de Kiro, un IDE IA basé sur le Spec-Driven Development (SDD).

- Lire @docs\SDD\Rapport-SDD-EARS.md pour comprendre les principes du Spec-Driven Development (SDD) et de EARS (Easy Approach to Requirements Syntax).

- Une fois en connaissance de toutes ces informations, élaborez un plan afin de créer des modes personnalisés pour RooCode et KiloCode qui intègrent les fonctionnalités de Kiro, du Spec-Driven Development (SDD) et de EARS.
- Elaborez un plan détaillé pour la création de "sets de modes personnalisés" pour RooCode et KiloCode qui reproduisent des métodes de développement logiciel divers comme AGILE, SCRUM, KANBAN, FDD (Feature Driven Development), TDD (Test Driven Development), BDD (Behavior Driven Development).
- Egalement pour la création de "sets de modes personnalisés" simulant des équipes de développement logiciel avec des rôles variés comme Product Owner, Scrum Master, Développeur, Testeur, Architecte, Analyste Fonctionnel, UX/UI Designer, DevOps, et tout autre rôle pertinent.


> Produisez un document détaillant les étapes nécessaires pour créer ces modes personnalisés, en incluant les spécifications, les outils nécessaires, et les meilleures pratiques à suivre dans `TEST-1\{toolName}\Plan.md`

---

TEST 1.1 - TRAYCER  Mode Plan > Simple Agent -> 
TEST 1.2 - TRAYCER - Mode Plan - Multi-Agent ->  693 Lignes
TEST 1.3 - TRAYCER  Mode Phases ->  7 Phases -> Simple Agent ->
TEST 1.3 - TRAYCER  Mode Phases -> 7 Phases -> Multi Agent ->

TEST 1.4 - AUGMENT - Mode Tasklist ->

TEST 1.5 - GITHUB COPILOT - Instructions Plan de Développement (Claude Sonnet 4) ->
TEST 1.6 - GITHUB COPILOT + ZEN-MCP-SERVER - Mode Planner (Claude Sonnet 4 + Gemini-2.5-Pro) ->

TEST 1.7 - CURSOR - Mode PLAN (Claude Sonnet 4) ->

TEST 1.8 - KILOCODE - Instructions Plan de Développement (Claude Sonnet 4) ->

TEST 1.9 - TRAE -  Instructions Plan de Développement (Claude Sonnet 4) ->

TEST 1.10 - KIRO - Mode Spec-Driven Development (Claude Sonnet 4) ->

TEST 1.11 - TASK-MASTER (Gemini-2.5-Pro) ->