I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

Based on my analysis of the RooCode and KiloCode custom modes system, I've identified that both tools use identical YAML schemas for custom modes configuration. The existing system already supports the core functionality needed for implementing SDD/EARS workflows and methodology-specific modes. The current `.roomodes` and `.kilocodemodes` files contain three existing modes that demonstrate the pattern for orchestration, web research, and data extraction. The documentation reveals a robust system for tool permissions, file restrictions, context loading, and integration with todo lists and MCP protocols.

### Approach

I'll create a comprehensive set of custom modes organized into three categories: Foundation SDD modes that implement Kiro-style Spec-Driven Development with EARS syntax, Methodology modes that embody specific software development approaches, and Role Persona modes that simulate team member perspectives. The implementation will leverage the existing YAML schema, tool permission system, and context loading mechanisms. Each mode will be carefully designed with appropriate permissions and clear boundaries to ensure they work together harmoniously while maintaining the principle of single responsibility.

### Reasoning

I analyzed the existing custom modes configuration files to understand the YAML schema structure used by both RooCode and KiloCode. I examined the tool permissions system to understand how modes can be granted different levels of access (read, edit, browser, command, mcp) and how file restrictions work through regex patterns. I studied the context management system including workspace rules, todo list integration, and template structures. I thoroughly reviewed the Kiro documentation and SDD/EARS report to understand the three-phase workflow and EARS syntax patterns that need to be embedded in the custom modes.

## Mermaid Diagram

```mermaid
sequenceDiagram
    participant User
    participant SDD_Req as SDD Requirements Writer
    participant SDD_Design as SDD Design Architect
    participant SDD_Task as SDD Task Orchestrator
    participant Method as Methodology Mode
    participant Role as Role Persona
    participant TodoList as Todo List System

    User->>SDD_Req: Request new feature
    SDD_Req->>SDD_Req: Generate requirements.md with EARS syntax
    SDD_Req->>User: Present requirements for approval
    
    User->>SDD_Design: Approve requirements
    SDD_Design->>SDD_Design: Analyze codebase + requirements
    SDD_Design->>SDD_Design: Generate design.md
    SDD_Design->>User: Present design for approval
    
    User->>SDD_Task: Approve design
    SDD_Task->>SDD_Task: Break down into tasks.md
    SDD_Task->>TodoList: Create todo list
    SDD_Task->>User: Present task plan
    
    User->>Method: Select methodology mode
    Method->>SDD_Task: Apply methodology-specific practices
    Method->>TodoList: Update with methodology workflow
    
    User->>Role: Consult role persona
    Role->>User: Provide domain-specific guidance
    Role->>SDD_Task: Influence task prioritization
    
    SDD_Task->>TodoList: Track progress
    TodoList->>User: Report completion status
```

## Proposed File Changes

### tools(NEW)

Create a tools directory to house automation scripts for managing custom modes. This directory will contain utilities for synchronizing mode definitions between RooCode and KiloCode, validating YAML syntax, and ensuring consistency across the mode ecosystem.

### tools\mode-sync.py(NEW)

References: 

- .roomodes(MODIFY)
- .kilocodemodes(MODIFY)

Create a Python script that synchronizes custom mode definitions between `.roomodes` and `.kilocodemodes` files. The script should:

- Read mode definitions from a single source of truth
- Validate YAML syntax and required fields (slug, name, roleDefinition, customInstructions, groups, source)
- Check for slug uniqueness to prevent conflicts
- Update both `.roomodes` and `.kilocodemodes` files with identical content
- Provide logging and error reporting for validation failures
- Support both individual mode updates and bulk synchronization

The script should use PyYAML for parsing and maintain the exact formatting and structure shown in the existing files. Include command-line arguments for different operations (sync, validate, add-mode).

### templates\modes(NEW)

Create a templates directory specifically for mode definitions. This will house template files that provide the canonical structure for creating new custom modes, ensuring consistency and completeness.

### templates\modes\mode-template.yaml(NEW)

References: 

- .roomodes(MODIFY)
- docs\roo-kilo\Roo-Customizing-Modes.md

Create a canonical YAML template for custom modes that includes all required and optional fields with documentation comments. The template should include:

- Complete field structure with type annotations in comments
- Examples for each field type
- Guidance on permission groups (read, edit, browser, command, mcp)
- File regex pattern examples for edit restrictions
- Best practices for customInstructions formatting
- References to the existing schema documentation

This template will serve as the starting point for all new mode creation and ensure consistency across the mode ecosystem.

### modes\sdd-foundation(NEW)

Create a directory for the foundational SDD (Spec-Driven Development) modes that implement the core Kiro-style workflow. These modes will serve as the building blocks for all other methodology and role-based modes.

### modes\sdd-foundation\sdd-requirements-writer.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\kiro\kiro.md

Create a mode definition for the SDD Requirements Writer that specializes in generating requirements.md files using EARS syntax. The mode should:

- Have slug: `sdd-requirements-writer`
- Name: `📋 SDD Requirements Writer`
- roleDefinition: Focus on eliciting and documenting requirements using EARS syntax patterns
- whenToUse: When starting a new feature or need to formalize requirements
- groups: `[read, mcp]` (advisory mode, no edit permissions)
- customInstructions: Include detailed guidance on:
  - The five EARS patterns (Ubiquitous, Event-Driven, State-Driven, Unwanted Behavior, Optional Feature)
  - Examples of each pattern with proper syntax
  - How to decompose user stories into EARS requirements
  - Integration with the SDD three-phase workflow
  - References to `docs/SDD/Rapport-SDD-EARS.md` for detailed methodology
  - Templates for requirements.md structure
  - Validation criteria for requirement quality

The mode should guide users through systematic requirements gathering while enforcing EARS syntax compliance.

### modes\sdd-foundation\sdd-design-architect.yaml(NEW)

References: 

- docs\kiro\kiro.md
- templates\features\exemple\design.md

Create a mode definition for the SDD Design Architect that generates design.md files from requirements and existing codebase analysis. The mode should:

- Have slug: `sdd-design-architect`
- Name: `🏗️ SDD Design Architect`
- roleDefinition: Analyzes requirements and existing code to produce technical design documents
- whenToUse: After requirements are finalized and before implementation begins
- groups: `[read, mcp]` (advisory mode, reads codebase but doesn't edit)
- customInstructions: Include guidance on:
  - Reading and analyzing requirements.md files with EARS syntax
  - Examining existing codebase structure and patterns
  - Creating architectural diagrams using Mermaid syntax
  - Defining interfaces, data models, and API endpoints
  - Integration patterns with existing systems
  - Error handling and edge case considerations
  - Performance and scalability considerations
  - References to `docs/kiro/kiro.md` for design phase methodology
  - Templates for design.md structure including architecture overview, component responsibilities, data flow diagrams

The mode should bridge the gap between business requirements and technical implementation.

### modes\sdd-foundation\sdd-task-orchestrator.yaml(NEW)

References: 

- .roomodes(MODIFY)
- docs\roo-kilo\Roo-Todo-List.md
- templates\features\exemple\tasks.md

Create a mode definition for the SDD Task Orchestrator that breaks down design into actionable tasks and manages the todo workflow. The mode should:

- Have slug: `sdd-task-orchestrator`
- Name: `🎯 SDD Task Orchestrator`
- roleDefinition: Converts design documents into granular, dependency-aware implementation plans and manages task execution
- whenToUse: After design approval to create implementation roadmap and during development to track progress
- groups: `[read, edit, mcp]` with fileRegex: `'\.(md|json)$'` (can edit documentation and configuration files)
- customInstructions: Include guidance on:
  - Reading requirements.md and design.md to understand scope
  - Breaking down features into atomic, testable tasks
  - Creating dependency graphs between tasks
  - Integration with the `update_todo_list` tool for progress tracking
  - Generating tasks.md with proper linking to requirements and design
  - Task estimation and priority assignment
  - Coordination with other modes for task execution
  - References to the existing `advanced-orchestrator` mode for delegation patterns
  - Templates for tasks.md structure with clear acceptance criteria
  - Progress monitoring and reporting capabilities

The mode should act as the central coordinator for the SDD workflow, managing the transition from planning to execution.

### modes\methodologies(NEW)

Create a directory for methodology-specific modes that implement various software development approaches. Each methodology mode will integrate with the SDD foundation while adding methodology-specific practices and workflows.

### modes\methodologies\agile-coach.yaml(NEW)

Create a mode definition for an Agile Coach that provides general agile methodology guidance. The mode should:

- Have slug: `agile-coach`
- Name: `🔄 Agile Coach`
- roleDefinition: Guides teams in agile principles, practices, and continuous improvement
- whenToUse: For general agile guidance, retrospectives, and process improvement
- groups: `[read, mcp]` (advisory role)
- customInstructions: Include guidance on:
  - Agile manifesto principles and values
  - Iterative development and feedback loops
  - User story writing and backlog management
  - Sprint planning and estimation techniques
  - Retrospective facilitation
  - Continuous improvement practices
  - Integration with SDD workflow for structured development
  - Coaching teams on agile adoption
  - Identifying and removing impediments
  - Metrics and measurement in agile environments

The mode should provide high-level agile guidance while encouraging the use of SDD foundation modes for detailed work.

### modes\methodologies\scrum-master.yaml(NEW)

References: 

- docs\roo-kilo\Roo-Todo-List.md

Create a mode definition for a Scrum Master that facilitates Scrum ceremonies and processes. The mode should:

- Have slug: `scrum-master`
- Name: `🏃 Scrum Master`
- roleDefinition: Facilitates Scrum ceremonies, removes impediments, and ensures Scrum process adherence
- whenToUse: For sprint planning, daily standups, sprint reviews, and retrospectives
- groups: `[read, edit, mcp]` with fileRegex: `'(sprint|backlog|retrospective).*\.(md|json)$'` (can edit sprint-related documents)
- customInstructions: Include guidance on:
  - Scrum framework and ceremonies
  - Sprint planning and goal setting
  - Daily standup facilitation
  - Sprint review and demo preparation
  - Retrospective techniques and action items
  - Backlog refinement and story estimation
  - Impediment identification and removal
  - Team velocity tracking and forecasting
  - Integration with todo list for sprint tracking
  - Coordination with SDD modes for feature development
  - Scrum metrics and reporting

The mode should enforce Scrum practices while leveraging SDD for structured feature development.

### modes\methodologies\kanban-manager.yaml(NEW)

Create a mode definition for a Kanban Manager that implements Kanban workflow principles. The mode should:

- Have slug: `kanban-manager`
- Name: `📊 Kanban Manager`
- roleDefinition: Manages Kanban boards, WIP limits, and continuous flow optimization
- whenToUse: For continuous delivery workflows and flow-based development
- groups: `[read, edit, mcp]` with fileRegex: `'(kanban|board|wip).*\.(md|json)$'` (can edit Kanban-related documents)
- customInstructions: Include guidance on:
  - Kanban principles and practices
  - Work-in-progress (WIP) limit management
  - Flow metrics and cycle time optimization
  - Continuous delivery and deployment
  - Visual workflow management
  - Bottleneck identification and resolution
  - Service level agreements and predictability
  - Integration with SDD for feature breakdown
  - Pull-based work assignment
  - Cumulative flow diagrams and analytics

The mode should optimize for continuous flow while maintaining SDD quality standards.

### modes\methodologies\fdd-coordinator.yaml(NEW)

Create a mode definition for a Feature-Driven Development (FDD) Coordinator. The mode should:

- Have slug: `fdd-coordinator`
- Name: `🎯 FDD Coordinator`
- roleDefinition: Coordinates feature-driven development with domain modeling and feature sets
- whenToUse: For large-scale projects requiring domain modeling and feature-centric development
- groups: `[read, edit, mcp]` with fileRegex: `'(feature|domain|model).*\.(md|json)$'` (can edit feature and domain documents)
- customInstructions: Include guidance on:
  - Domain modeling and object-oriented analysis
  - Feature list development and categorization
  - Feature set planning and scheduling
  - Class ownership and chief programmer teams
  - Design by feature and build by feature processes
  - Integration with SDD for detailed feature specification
  - Progress tracking by feature completion
  - Domain expertise identification and utilization
  - Feature dependency management
  - Quality assurance at the feature level

The mode should coordinate large-scale development while leveraging SDD for individual feature implementation.

### modes\methodologies\tdd-guide.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md

Create a mode definition for a Test-Driven Development (TDD) Guide. The mode should:

- Have slug: `tdd-guide`
- Name: `🧪 TDD Guide`
- roleDefinition: Guides test-driven development practices with red-green-refactor cycles
- whenToUse: For implementing TDD practices and ensuring comprehensive test coverage
- groups: `[read, edit, mcp]` with fileRegex: `'(test|spec).*\.(js|ts|py|java|cs|rb)$'` (can edit test files)
- customInstructions: Include guidance on:
  - Red-Green-Refactor cycle implementation
  - Writing failing tests first
  - Minimal code to pass tests
  - Refactoring with confidence
  - Test design patterns and best practices
  - Integration with SDD requirements for test scenarios
  - EARS requirements translation to test cases
  - Test coverage and quality metrics
  - Unit, integration, and acceptance testing strategies
  - Mocking and test isolation techniques
  - Continuous testing and feedback loops

The mode should enforce TDD discipline while integrating with EARS requirements for comprehensive test coverage.

### modes\methodologies\bdd-facilitator.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md

Create a mode definition for a Behavior-Driven Development (BDD) Facilitator. The mode should:

- Have slug: `bdd-facilitator`
- Name: `🎭 BDD Facilitator`
- roleDefinition: Facilitates behavior-driven development with Given-When-Then scenarios
- whenToUse: For collaborative specification and acceptance criteria definition
- groups: `[read, edit, mcp]` with fileRegex: `'(feature|scenario|step).*\.(feature|md|js|ts|py)$'` (can edit BDD-related files)
- customInstructions: Include guidance on:
  - Given-When-Then scenario writing
  - Collaborative specification workshops
  - Living documentation with executable specifications
  - Acceptance criteria definition and validation
  - Integration with EARS syntax for behavior specification
  - Cucumber/Gherkin syntax and best practices
  - Example mapping and scenario discovery
  - Stakeholder collaboration and communication
  - Automated acceptance testing
  - Behavior specification refinement

The mode should bridge business requirements and technical implementation through collaborative behavior specification.

### modes\roles(NEW)

Create a directory for role-based persona modes that simulate different team member perspectives. These modes will provide domain-specific expertise and viewpoints while remaining orthogonal to methodology choices.

### modes\roles\product-owner.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md

Create a mode definition for a Product Owner persona. The mode should:

- Have slug: `role-product-owner`
- Name: `💼 Product Owner`
- roleDefinition: Represents business value perspective, prioritizes features, and defines acceptance criteria
- whenToUse: For business requirements clarification, feature prioritization, and acceptance criteria definition
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - Business value assessment and ROI analysis
  - User story writing and acceptance criteria
  - Feature prioritization and backlog management
  - Stakeholder communication and requirements gathering
  - Market analysis and competitive positioning
  - User experience and customer journey mapping
  - Integration with EARS syntax for clear requirements
  - Product roadmap planning and vision communication
  - Metrics definition and success criteria
  - Risk assessment and mitigation strategies

The mode should focus on business value and user needs while supporting technical teams with clear requirements.

### modes\roles\scrum-master-role.yaml(NEW)

Create a mode definition for a Scrum Master role persona (distinct from the methodology mode). The mode should:

- Have slug: `role-scrum-master`
- Name: `🏃‍♂️ Scrum Master (Role)`
- roleDefinition: Focuses on team dynamics, process improvement, and impediment removal from a servant leadership perspective
- whenToUse: For team coaching, process guidance, and organizational impediment identification
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - Servant leadership and team empowerment
  - Facilitation techniques and meeting management
  - Conflict resolution and team dynamics
  - Organizational impediment identification
  - Change management and agile transformation
  - Coaching and mentoring approaches
  - Team performance and health metrics
  - Stakeholder management and communication
  - Process improvement and retrospective facilitation
  - Agile maturity assessment and growth planning

The mode should focus on team enablement and process optimization rather than direct process execution.

### modes\roles\developer.yaml(NEW)

References: 

- docs\kiro\kiro.md

Create a mode definition for a Developer persona. The mode should:

- Have slug: `role-developer`
- Name: `👨‍💻 Developer`
- roleDefinition: Provides technical implementation perspective, code quality focus, and engineering best practices
- whenToUse: For technical feasibility assessment, implementation planning, and code quality guidance
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - Technical feasibility analysis and estimation
  - Code quality standards and best practices
  - Architecture patterns and design principles
  - Performance optimization and scalability considerations
  - Security best practices and vulnerability assessment
  - Code review guidelines and feedback
  - Technical debt identification and management
  - Integration with SDD for implementation planning
  - Testing strategies and quality assurance
  - Documentation and knowledge sharing

The mode should provide technical expertise while supporting structured development through SDD practices.

### modes\roles\tester.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md

Create a mode definition for a Tester persona. The mode should:

- Have slug: `role-tester`
- Name: `🔍 Tester`
- roleDefinition: Focuses on quality assurance, test strategy, and defect prevention from a QA perspective
- whenToUse: For test planning, quality strategy, and defect analysis
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - Test strategy development and planning
  - Test case design and coverage analysis
  - Defect identification and root cause analysis
  - Quality metrics and reporting
  - Risk-based testing approaches
  - Automation strategy and tool selection
  - Performance and security testing considerations
  - Integration with EARS requirements for test scenarios
  - User acceptance testing and validation
  - Continuous testing and quality gates

The mode should ensure comprehensive quality coverage while integrating with SDD requirements and design specifications.

### modes\roles\architect.yaml(NEW)

References: 

- docs\kiro\kiro.md

Create a mode definition for an Architect persona. The mode should:

- Have slug: `role-architect`
- Name: `🏛️ Architect`
- roleDefinition: Provides system architecture perspective, design patterns, and long-term technical vision
- whenToUse: For architectural decisions, system design, and technical strategy
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - System architecture design and patterns
  - Technology stack selection and evaluation
  - Scalability and performance architecture
  - Security architecture and compliance
  - Integration patterns and API design
  - Data architecture and modeling
  - Cloud architecture and deployment strategies
  - Technical debt management and refactoring
  - Architecture documentation and communication
  - Long-term technical vision and roadmap

The mode should provide high-level architectural guidance while supporting detailed design through SDD processes.

### modes\roles\analyst.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md

Create a mode definition for a Functional Analyst persona. The mode should:

- Have slug: `role-analyst`
- Name: `📊 Functional Analyst`
- roleDefinition: Specializes in requirements analysis, business process modeling, and stakeholder communication
- whenToUse: For requirements gathering, business analysis, and process documentation
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - Requirements elicitation and analysis techniques
  - Business process modeling and optimization
  - Stakeholder identification and management
  - Gap analysis and impact assessment
  - Use case development and scenario planning
  - Data flow analysis and documentation
  - Integration with EARS syntax for precise requirements
  - Traceability matrix development
  - Change impact analysis and management
  - Business rule definition and validation

The mode should bridge business needs and technical solutions through systematic analysis and clear documentation.

### modes\roles\ux-designer.yaml(NEW)

Create a mode definition for a UX/UI Designer persona. The mode should:

- Have slug: `role-ux-designer`
- Name: `🎨 UX/UI Designer`
- roleDefinition: Focuses on user experience, interface design, and usability from a design perspective
- whenToUse: For user experience planning, interface design, and usability considerations
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - User research and persona development
  - User journey mapping and experience design
  - Interface design principles and patterns
  - Usability testing and validation
  - Accessibility standards and compliance
  - Design system development and maintenance
  - Prototyping and wireframing techniques
  - Visual design and branding consistency
  - Mobile and responsive design considerations
  - Design-development collaboration and handoff

The mode should ensure user-centered design while integrating with technical requirements and constraints.

### modes\roles\devops.yaml(NEW)

Create a mode definition for a DevOps Engineer persona. The mode should:

- Have slug: `role-devops`
- Name: `⚙️ DevOps Engineer`
- roleDefinition: Provides infrastructure, deployment, and operational perspective with focus on automation and reliability
- whenToUse: For deployment planning, infrastructure design, and operational considerations
- groups: `[read]` (advisory persona, read-only access)
- customInstructions: Include guidance on:
  - CI/CD pipeline design and optimization
  - Infrastructure as code and automation
  - Monitoring and observability strategies
  - Security and compliance in deployment
  - Scalability and performance optimization
  - Disaster recovery and backup strategies
  - Container orchestration and microservices
  - Cloud platform optimization and cost management
  - Release management and deployment strategies
  - Operational excellence and incident response

The mode should ensure operational readiness while supporting development workflows and quality standards.

### .roomodes(MODIFY)

References: 

- modes\sdd-foundation\sdd-requirements-writer.yaml(NEW)
- modes\sdd-foundation\sdd-design-architect.yaml(NEW)
- modes\sdd-foundation\sdd-task-orchestrator.yaml(NEW)

Update the main RooCode custom modes configuration file to include all the new SDD foundation modes, methodology modes, and role persona modes. The new modes should be added to the existing `customModes` array after the current three modes (advanced-orchestrator, fire-crawler, deep-research).

Add the following modes in this order:

**SDD Foundation Modes:**
- sdd-requirements-writer
- sdd-design-architect  
- sdd-task-orchestrator

**Methodology Modes:**
- agile-coach
- scrum-master
- kanban-manager
- fdd-coordinator
- tdd-guide
- bdd-facilitator

**Role Persona Modes:**
- role-product-owner
- role-scrum-master
- role-developer
- role-tester
- role-architect
- role-analyst
- role-ux-designer
- role-devops

Each mode should be added with the complete YAML structure including all required fields (slug, name, roleDefinition, whenToUse, description, customInstructions, groups, source). The content should match the individual YAML files created in the modes directories, but consolidated into the main configuration file format.

Maintain the existing YAML formatting style and ensure proper indentation and structure consistency with the existing modes.

### .kilocodemodes(MODIFY)

References: 

- .roomodes(MODIFY)

Update the KiloCode custom modes configuration file to exactly match the updated `.roomodes` file. This ensures complete synchronization between RooCode and KiloCode configurations.

The file should contain identical content to the updated `.roomodes` file, including:
- All existing modes (advanced-orchestrator, fire-crawler, deep-research)
- All new SDD foundation modes
- All new methodology modes  
- All new role persona modes

Maintain identical YAML structure, formatting, and content to ensure both IDEs have the same custom modes available. This file should be kept in sync with `.roomodes` through the synchronization script.

### docs\modes(NEW)

Create a documentation directory for the custom modes ecosystem. This will house comprehensive documentation for each mode category, usage guidelines, and integration examples.

### docs\modes\README.md(NEW)

References: 

- docs\roo-kilo\Roo-Customizing-Modes.md

Create a comprehensive overview document for the custom modes ecosystem. The document should include:

- Introduction to the three categories of modes (SDD Foundation, Methodologies, Role Personas)
- Quick start guide for using the modes
- Integration patterns between different mode types
- Examples of common workflows combining multiple modes
- References to detailed documentation for each category
- Troubleshooting and FAQ section
- Links to the community gallery and contribution guidelines

The document should serve as the entry point for users wanting to understand and use the custom modes system effectively.

### docs\modes\sdd-foundation.md(NEW)

References: 

- docs\kiro\kiro.md
- docs\SDD\Rapport-SDD-EARS.md

Create detailed documentation for the SDD Foundation modes. The document should include:

- Overview of the Spec-Driven Development workflow
- Detailed explanation of each foundation mode (requirements writer, design architect, task orchestrator)
- EARS syntax reference and examples
- Integration with the three-phase SDD process
- Usage examples and best practices
- Integration with todo lists and MCP systems
- Troubleshooting common issues
- References to the Kiro documentation and SDD report

The document should enable users to effectively implement SDD workflows using the foundation modes.

### docs\modes\methodologies.md(NEW)

Create detailed documentation for the methodology modes. The document should include:

- Overview of each methodology (Agile, Scrum, Kanban, FDD, TDD, BDD)
- Specific mode capabilities and use cases
- Integration patterns with SDD foundation modes
- Methodology-specific workflows and best practices
- Examples of combining methodologies with role personas
- Configuration guidance for team-specific needs
- Comparison matrix of methodology approaches
- Migration guidance between methodologies

The document should help teams choose and implement appropriate methodologies for their context.

### docs\modes\role-personas.md(NEW)

Create detailed documentation for the role persona modes. The document should include:

- Overview of each role persona and their expertise areas
- When and how to engage different role perspectives
- Integration with methodology and foundation modes
- Examples of multi-role collaboration scenarios
- Customization guidance for organization-specific roles
- Best practices for role-based development workflows
- Communication patterns between different roles
- Conflict resolution when roles have different perspectives

The document should enable effective use of role-based perspectives in development workflows.

### docs\modes\integration-examples.md(NEW)

References: 

- docs\roo-kilo\Roo-Todo-List.md
- docs\roo-kilo\Roo-Custom-Instructions.md

Create a comprehensive guide with practical examples of how to combine different modes for complete development workflows. The document should include:

- End-to-end workflow examples combining SDD, methodology, and role modes
- Scenario-based usage patterns (e.g., new feature development, bug fixing, refactoring)
- Mode transition guidelines and handoff procedures
- Team collaboration patterns using multiple modes
- Integration with existing tools and processes
- Performance and efficiency optimization tips
- Common pitfalls and how to avoid them
- Advanced usage patterns for experienced teams

The document should provide practical guidance for implementing comprehensive AI-assisted development workflows.