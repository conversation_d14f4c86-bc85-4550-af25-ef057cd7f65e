
## PHASE 2

I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the existing structure and found that `.roomodes` and `.kilocodemodes` are identical YAML files containing custom modes. The repository has a `modes/` directory with subdirectories for specific modes, and a `templates/` directory with context templates. There's no existing `tools/` directory or synchronization scripts. The custom modes follow a specific YAML structure with properties like slug, name, roleDefinition, customInstructions, groups, and source.

### Approach

I'll create a `tools/` directory with a Python script `mode-sync.py` that ensures synchronization between `.roomodes` and `.kilocodemodes` files. The script will use PyYAML for safe file operations and include validation. I'll also create a `templates/modes/` directory with a canonical `mode-template.yaml` that follows the established patterns from existing modes, providing a standardized template for creating new custom modes.

### Reasoning

I examined the repository structure and found the existing `.roomodes` and `.kilocodemodes` files are identical YAML configurations. I analyzed the custom mode structure from existing files and documentation. I searched for existing tools or scripts but found none. I researched Python YAML synchronization best practices to inform the script design.

## Mermaid Diagram

sequenceDiagram
    participant User as Utilisateur
    participant Script as mode-sync.py
    participant RooModes as .roomodes
    participant KiloModes as .kilocodemodes
    participant Backup as Fichiers de sauvegarde
    participant Template as mode-template.yaml

    User->>Script: Exécuter synchronisation
    Script->>RooModes: Charger contenu YAML
    Script->>KiloModes: Charger contenu YAML
    Script->>Script: Comparer les configurations
    
    alt Différences détectées
        Script->>Backup: Créer sauvegardes
        Script->>Script: Résoudre les conflits
        Script->>RooModes: Écrire version synchronisée
        Script->>KiloModes: Écrire version synchronisée
        Script->>Script: Valider structure YAML
    else Aucune différence
        Script->>User: Fichiers déjà synchronisés
    end
    
    User->>Template: Créer nouveau mode
    Template->>User: Structure YAML canonique
    User->>Script: Valider nouveau mode
    Script->>Script: Vérifier structure
    Script->>User: Confirmation validation

## Proposed File Changes

### tools(NEW)

Create the `tools/` directory to house utility scripts for mode management and synchronization.

### tools\mode-sync.py(NEW)

References: 

- .roomodes
- .kilocodemodes

Create a Python script for synchronizing modes between RooCode and KiloCode. The script will:

- Use `yaml.safe_load()` and `yaml.dump()` for safe YAML operations
- Compare `.roomodes` and `.kilocodemodes` files for differences
- Synchronize content bidirectionally with conflict detection
- Validate YAML structure after synchronization
- Include backup functionality before making changes
- Provide verbose logging and error handling
- Support dry-run mode for testing
- Include functions to:
  - `load_yaml_file(filepath)`: Safely load YAML files
  - `save_yaml_file(filepath, data)`: Safely save YAML files
  - `compare_modes(roomodes_data, kilocodemodes_data)`: Compare mode configurations
  - `sync_files(source_file, target_file)`: Synchronize files
  - `validate_mode_structure(mode_data)`: Validate mode YAML structure
  - `create_backup(filepath)`: Create backup before changes
  - `main()`: CLI interface with argparse for different operations

The script will follow the existing YAML structure patterns found in `.roomodes` and `.kilocodemodes` files, ensuring compatibility with the current mode system.

### templates\modes(NEW)

Create the `templates/modes/` directory to house mode templates for standardized mode creation.

### templates\modes\mode-template.yaml(NEW)

References: 

- .roomodes
- modes\firecrawl\.roomodes
- docs\roo-kilo\Roo-Customizing-Modes.md

Create a canonical template for new custom modes following the established YAML structure. The template will include:

- Complete YAML structure with all required properties
- Placeholder values using bracket notation (e.g., `[Mode Name]`, `[Mode Slug]`)
- Comments explaining each property and its purpose
- Examples of different group configurations (read, edit, mcp, browser, command)
- Sample customInstructions with markdown formatting
- Examples of fileRegex patterns for edit restrictions
- Multiple mode examples showing different use cases:
  - Basic mode with read/edit permissions
  - MCP-enabled mode for external tool integration
  - Specialized mode with file restrictions
- Documentation comments explaining:
  - Slug naming conventions (kebab-case)
  - Name formatting with emojis
  - roleDefinition vs customInstructions differences
  - whenToUse for orchestration guidance
  - Group permission types and combinations
  - Source property usage

The template will be based on the patterns observed in existing modes like `advanced-orchestrator`, `fire-crawler`, and `deep-research` from `.roomodes` and the modular mode structure found in `modes/firecrawl/.roomodes`.

### tools\README.md(NEW)

References: 

- docs\roo-kilo\Roo-Customizing-Modes.md

Create documentation for the tools directory explaining:

- Purpose of the tools directory
- Overview of `mode-sync.py` script functionality
- Usage examples and command-line options
- Prerequisites (Python, PyYAML dependency)
- Installation instructions
- Common use cases:
  - Synchronizing after manual edits
  - Validating mode configurations
  - Creating backups before changes
  - Dry-run testing
- Integration with the existing mode system
- Troubleshooting common issues
- Best practices for mode synchronization

Reference the existing documentation structure found in `docs/roo-kilo/Roo-Customizing-Modes.md` for consistency.

---

## PHASE 3

I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The repository has a well-established mode system with YAML configuration files. The existing modes show patterns for MCP integration, permission groups, and detailed custom instructions. The SDD documentation provides comprehensive information about EARS syntax and the three-phase workflow. The requirements template shows French EARS keywords like "QUAND", "ALORS", "TANDIS QUE", "SI". The user wants to create three specialized modes that implement the SDD methodology within the RooCode framework.

### Approach

I'll create the `modes/sdd-foundation/` directory with three foundational modes that integrate EARS syntax and Kiro's three-phase workflow. Each mode will be specialized for a specific phase: requirements writing, design architecture, and task orchestration. The modes will follow the established YAML structure and include comprehensive instructions for SDD methodology, EARS syntax patterns, and integration with the existing RooCode ecosystem.

### Reasoning

I analyzed the existing mode structure in `.roomodes` and `modes/firecrawl/.roomodes` to understand the YAML format and patterns. I examined the SDD/EARS documentation to understand the methodology and syntax requirements. I reviewed the Kiro documentation to understand the three-phase workflow (requirements → design → tasks). I studied the existing requirements template to see the EARS patterns in French. I identified that the user wants to create foundational modes that integrate these methodologies into the RooCode ecosystem.

## Mermaid Diagram

sequenceDiagram
    participant User as Utilisateur
    participant ReqWriter as SDD Requirements Writer
    participant DesignArch as SDD Design Architect
    participant TaskOrch as SDD Task Orchestrator
    participant TodoList as Todo List System
    participant Files as Fichiers de Projet

    User->>ReqWriter: Demande de fonctionnalité
    ReqWriter->>ReqWriter: Analyse et décomposition
    ReqWriter->>Files: Génère requirements.md (EARS)
    ReqWriter->>User: Présente exigences structurées

    User->>DesignArch: Valide requirements.md
    DesignArch->>Files: Lit requirements.md
    DesignArch->>DesignArch: Analyse architecture existante
    DesignArch->>Files: Génère design.md (Mermaid)
    DesignArch->>User: Présente conception technique

    User->>TaskOrch: Valide design.md
    TaskOrch->>Files: Lit requirements.md + design.md
    TaskOrch->>TaskOrch: Décompose en tâches
    TaskOrch->>Files: Génère tasks.md
    TaskOrch->>TodoList: Crée todo list structurée
    TaskOrch->>User: Présente plan d'implémentation

    User->>TaskOrch: Lance exécution
    TaskOrch->>TodoList: Met à jour statuts des tâches
    TaskOrch->>Files: Coordonne implémentation
    TaskOrch->>User: Rapport de progression

## Proposed File Changes

### modes\sdd-foundation(NEW)

Create the `modes/sdd-foundation/` directory to house the three foundational SDD modes that implement the Spec-Driven Development methodology with EARS syntax integration.

### modes\sdd-foundation\sdd-requirements-writer.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- templates\features\exemple\requirements.md

Create the Requirements Writer mode specialized in Phase 1 of the SDD workflow. This mode will:

- Focus on eliciting and structuring requirements using EARS syntax
- Transform user stories into structured requirements with acceptance criteria
- Implement the five EARS patterns: Ubiquitous, Event-Driven, State-Driven, Unwanted Behavior, Optional Feature
- Use French EARS keywords: "QUAND" (When), "ALORS" (Then), "TANDIS QUE" (While), "SI" (If), "OÙ" (Where)
- Generate `requirements.md` files following the template structure from `templates/features/exemple/requirements.md`
- Include comprehensive instructions for:
  - EARS syntax patterns and usage
  - User story decomposition
  - Acceptance criteria formulation
  - Requirements validation and review
  - Integration with the next phase (design)
- Provide examples of each EARS pattern in French
- Include validation guidelines for requirements quality
- Support both standalone requirements writing and integration with the SDD workflow
- Reference the SDD documentation patterns from `docs/SDD/Rapport-SDD-EARS.md`

The mode will have `read`, `edit`, and `mcp` permissions to allow full requirements documentation creation and management.

### modes\sdd-foundation\sdd-design-architect.yaml(NEW)

References: 

- docs\kiro\kiro.md
- templates\features\exemple\design.md

Create the Design Architect mode specialized in Phase 2 of the SDD workflow. This mode will:

- Focus on technical design and architecture planning based on requirements
- Analyze existing codebase and integrate new features seamlessly
- Generate `design.md` files with technical specifications
- Create architecture diagrams using Mermaid syntax
- Define interfaces, APIs, and data schemas
- Include comprehensive instructions for:
  - Technical architecture analysis
  - Integration with existing systems
  - Database schema design
  - API endpoint specification
  - Component interaction design
  - Performance and scalability considerations
  - Security and compliance requirements
- Support Mermaid diagram generation for:
  - System architecture diagrams
  - Data flow diagrams
  - Component interaction diagrams
  - Database entity relationships
- Bridge requirements from Phase 1 to implementation tasks in Phase 3
- Reference the Kiro design phase methodology from `docs/kiro/kiro.md`
- Include validation guidelines for design quality and completeness
- Support both standalone design work and integration with the SDD workflow

The mode will have `read`, `edit`, and `mcp` permissions to allow full design documentation creation and codebase analysis.

### modes\sdd-foundation\sdd-task-orchestrator.yaml(NEW)

References: 

- docs\kiro\kiro.md
- docs\roo-kilo\Roo-Todo-List.md
- templates\features\exemple\tasks.md

Create the Task Orchestrator mode specialized in Phase 3 of the SDD workflow. This mode will:

- Focus on breaking down design into actionable implementation tasks
- Generate `tasks.md` files with detailed implementation plans
- Create dependency-aware task sequences
- Integrate with RooCode's todo list functionality from `docs/roo-kilo/Roo-Todo-List.md`
- Include comprehensive instructions for:
  - Task decomposition and granularity
  - Dependency management and sequencing
  - Implementation priority and risk assessment
  - Test case generation for each task
  - Error handling and edge case considerations
  - Production readiness checklist items
  - Code review and quality assurance steps
- Support todo list creation and management using the `update_todo_list` tool
- Provide task status tracking (Pending, In Progress, Completed)
- Include validation guidelines for task completeness and feasibility
- Bridge design specifications to concrete implementation steps
- Support both standalone task planning and integration with the SDD workflow
- Reference the Kiro task phase methodology from `docs/kiro/kiro.md`
- Include examples of task breakdown for different types of features
- Support integration with existing orchestration patterns from the `advanced-orchestrator` mode

The mode will have `read`, `edit`, and `mcp` permissions to allow full task management and coordination capabilities.

### modes\sdd-foundation\.roomodes(NEW)

References: 

- .roomodes
- modes\firecrawl\.roomodes

Create the modular `.roomodes` configuration file for the SDD foundation modes. This file will:

- Follow the same YAML structure as the main `.roomodes` file
- Include all three SDD foundation modes in a single configuration
- Maintain consistency with the existing mode patterns
- Use the `customModes` array structure
- Include proper `source: project` designation
- Reference the individual YAML files for detailed mode definitions
- Provide a centralized configuration for the SDD foundation module
- Enable easy integration with the main configuration files
- Support the modular mode architecture established in `modes/firecrawl/.roomodes`

The file will contain the three modes:
1. `sdd-requirements-writer` - Phase 1: Requirements with EARS syntax
2. `sdd-design-architect` - Phase 2: Technical design and architecture
3. `sdd-task-orchestrator` - Phase 3: Task breakdown and orchestration

Each mode will have appropriate permissions and follow the established patterns from existing modes like `advanced-orchestrator`, `fire-crawler`, and `deep-research`.

### modes\sdd-foundation\README.md(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\kiro\kiro.md
- docs\roo-kilo\Roo-Todo-List.md

Create comprehensive documentation for the SDD Foundation modes. This README will:

- Explain the Spec-Driven Development (SDD) methodology and its three phases
- Document the EARS (Easy Approach to Requirements Syntax) integration
- Provide usage examples for each of the three modes
- Explain the workflow integration between phases
- Include practical examples of the complete SDD workflow
- Reference the theoretical foundations from `docs/SDD/Rapport-SDD-EARS.md` and `docs/kiro/kiro.md`
- Provide best practices for using the modes effectively
- Include troubleshooting and common usage patterns
- Document the integration with RooCode's existing features like todo lists
- Explain how the modes complement existing orchestration capabilities
- Provide templates and examples for each phase output
- Include guidelines for team adoption and workflow integration

The documentation will cover:
1. **Overview**: SDD methodology and benefits
2. **Phase 1**: Requirements writing with EARS syntax
3. **Phase 2**: Design and architecture planning
4. **Phase 3**: Task orchestration and implementation
5. **Integration**: How the phases work together
6. **Examples**: Complete workflow demonstrations
7. **Best Practices**: Tips for effective usage
8. **Troubleshooting**: Common issues and solutions

---

## PHASE 4

I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The repository has a well-established mode system with YAML configuration files following a specific structure. The SDD documentation provides comprehensive information about EARS syntax and the three-phase workflow (Requirements → Design → Tasks). The existing modes show patterns for MCP integration, permission groups, and detailed custom instructions. The user wants to create six specialized methodology modes that implement different software development approaches while integrating with the SDD methodology. Each mode should provide methodology-specific guidance while respecting the SDD foundations.

### Approach

I'll create the `modes/methodologies/` directory with six methodology-specific modes that integrate with the SDD foundations. Each mode will represent a different software development methodology (Agile, Scrum, Kanban, FDD, TDD, BDD) while seamlessly integrating with the established SDD/EARS approach. The modes will follow the existing YAML structure patterns and include comprehensive instructions for methodology-specific practices, ceremony automation, and integration with the three-phase SDD workflow (Requirements → Design → Tasks).

### Reasoning

I analyzed the existing mode structure from `.roomodes` and `modes/firecrawl/.roomodes` to understand the YAML format and patterns. I examined the comprehensive SDD/EARS documentation to understand the methodology and three-phase workflow. I researched each of the six methodologies (Agile, Scrum, Kanban, FDD, TDD, BDD) through web searches to understand their core principles and practices. I reviewed the todo list integration and MCP tool usage patterns. I confirmed that no SDD foundation modes exist yet and that the tools directory hasn't been created, so these methodology modes will be the first to implement SDD integration patterns.

## Mermaid Diagram

sequenceDiagram
    participant User as Utilisateur
    participant MethodMode as Mode Méthodologie
    participant SDD_Req as SDD Requirements Writer
    participant SDD_Design as SDD Design Architect
    participant SDD_Task as SDD Task Orchestrator
    participant TodoList as Todo List System
    participant Files as Fichiers de Projet

    User->>MethodMode: Demande d'implémentation méthodologique
    MethodMode->>MethodMode: Analyse méthodologie appropriée
    
    alt Agile/Scrum/Kanban
        MethodMode->>SDD_Req: Délègue création user stories (EARS)
        SDD_Req->>Files: Génère requirements.md
        MethodMode->>SDD_Design: Délègue conception sprint/feature
        SDD_Design->>Files: Génère design.md
        MethodMode->>SDD_Task: Délègue planification tâches
        SDD_Task->>Files: Génère tasks.md
        SDD_Task->>TodoList: Crée backlog/sprint board
    else TDD/BDD
        MethodMode->>SDD_Req: Délègue spécifications testables
        SDD_Req->>Files: Génère requirements.md (EARS/Gherkin)
        MethodMode->>SDD_Design: Délègue architecture de tests
        SDD_Design->>Files: Génère design.md (test patterns)
        MethodMode->>SDD_Task: Délègue cycle Red-Green-Refactor
        SDD_Task->>Files: Génère tasks.md (TDD/BDD workflow)
    else FDD
        MethodMode->>SDD_Req: Délègue modélisation domaine
        SDD_Req->>Files: Génère requirements.md (features)
        MethodMode->>SDD_Design: Délègue conception par feature
        SDD_Design->>Files: Génère design.md (domain model)
        MethodMode->>SDD_Task: Délègue planification features
        SDD_Task->>Files: Génère tasks.md (feature breakdown)
    end
    
    MethodMode->>User: Présente workflow méthodologique intégré
    MethodMode->>TodoList: Configure suivi méthodologique
    MethodMode->>User: Guide cérémonies et pratiques

## Proposed File Changes

### modes\methodologies(NEW)

Create the `modes/methodologies/` directory to house the six methodology-specific modes that integrate software development methodologies with the SDD foundations.

### modes\methodologies\agile-coach.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\roo-kilo\Roo-Todo-List.md

Create the Agile Coach mode that integrates Agile methodology principles with SDD foundations. This mode will:

- Focus on Agile principles: customer collaboration, responding to change, working software, individuals and interactions
- Implement the 12 Agile principles from the Agile Manifesto
- Guide iterative development with short sprints and frequent delivery
- Facilitate daily stand-ups, sprint planning, reviews, and retrospectives
- Integrate with SDD workflow by:
  - Using `sdd-requirements-writer` for user story creation with EARS syntax
  - Leveraging `sdd-design-architect` for technical planning within sprints
  - Coordinating with `sdd-task-orchestrator` for backlog management
- Provide guidance on:
  - Backlog prioritization and user story refinement
  - Sprint planning and estimation techniques
  - Agile ceremonies and their integration with SDD phases
  - Continuous improvement and adaptation
  - Cross-functional team collaboration
  - Customer feedback integration
- Include examples of Agile practices adapted for SDD methodology
- Support todo list management for sprint tracking using `update_todo_list` tool
- Reference the Agile Manifesto principles and modern Agile practices

The mode will have `read`, `edit`, and `mcp` permissions to allow full project management and coordination capabilities.

### modes\methodologies\scrum-master.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\roo-kilo\Roo-Todo-List.md

Create the Scrum Master mode that implements the Scrum framework with SDD integration. This mode will:

- Implement the three Scrum roles: Product Owner guidance, Scrum Master facilitation, Development Team support
- Manage the four key Scrum events: Sprint Planning, Daily Scrum, Sprint Review, Sprint Retrospective
- Handle the three Scrum artifacts: Product Backlog, Sprint Backlog, Increment
- Integrate with SDD workflow by:
  - Using `sdd-requirements-writer` for Product Backlog item creation with EARS acceptance criteria
  - Leveraging `sdd-design-architect` for Sprint Planning technical analysis
  - Coordinating with `sdd-task-orchestrator` for Sprint Backlog management
- Provide guidance on:
  - Sprint planning and backlog refinement
  - Daily Scrum facilitation and impediment removal
  - Sprint Review and stakeholder feedback integration
  - Sprint Retrospective and continuous improvement
  - Definition of Done and acceptance criteria
  - Velocity tracking and sprint metrics
  - Scrum ceremonies timing and facilitation
- Include templates for Scrum artifacts adapted for SDD methodology
- Support sprint tracking and burndown management using todo lists
- Implement Scrum best practices for team self-organization
- Reference official Scrum Guide principles and practices

The mode will have `read`, `edit`, and `mcp` permissions to allow full Scrum ceremony facilitation and artifact management.

### modes\methodologies\kanban-manager.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\roo-kilo\Roo-Todo-List.md

Create the Kanban Manager mode that implements Kanban methodology with SDD integration. This mode will:

- Implement the four Kanban principles: start with current process, pursue incremental change, respect current roles, encourage leadership at all levels
- Apply the six Kanban practices: visualize workflow, limit WIP, manage flow, make policies explicit, implement feedback loops, improve collaboratively
- Integrate with SDD workflow by:
  - Using `sdd-requirements-writer` for work item definition with EARS criteria
  - Leveraging `sdd-design-architect` for workflow design and optimization
  - Coordinating with `sdd-task-orchestrator` for continuous flow management
- Provide guidance on:
  - Kanban board design and column configuration
  - Work-in-Progress (WIP) limits setting and enforcement
  - Flow metrics and cycle time optimization
  - Bottleneck identification and resolution
  - Continuous delivery and deployment
  - Service level agreements and predictability
  - Visual management and transparency
- Include templates for Kanban boards adapted for SDD phases
- Support flow visualization and metrics tracking
- Implement Kanban policies for different work types
- Focus on continuous improvement without disruptive changes
- Reference Kanban principles and modern flow-based practices

The mode will have `read`, `edit`, and `mcp` permissions to allow full workflow management and optimization capabilities.

### modes\methodologies\fdd-coordinator.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\roo-kilo\Roo-Todo-List.md

Create the Feature Driven Development (FDD) Coordinator mode that implements FDD methodology with SDD integration. This mode will:

- Implement the five FDD processes: Develop Overall Model, Build Features List, Plan by Feature, Design by Feature, Build by Feature
- Focus on feature-centric development with client-valued functionality
- Integrate with SDD workflow by:
  - Using `sdd-requirements-writer` for feature definition with EARS syntax
  - Leveraging `sdd-design-architect` for domain modeling and feature design
  - Coordinating with `sdd-task-orchestrator` for feature-based task breakdown
- Provide guidance on:
  - Domain modeling and overall system architecture
  - Feature list creation and prioritization
  - Feature ownership and team assignment
  - Two-week feature delivery cycles
  - Feature design and peer review processes
  - Feature progress tracking and reporting
  - Integration with existing systems
- Include templates for FDD artifacts adapted for SDD methodology:
  - Domain models and feature lists
  - Feature cards and design documents
  - Progress tracking and milestone reports
- Support feature-based planning and delivery
- Implement FDD best practices for larger teams
- Focus on tangible, client-valued deliverables
- Reference FDD methodology and feature-driven practices

The mode will have `read`, `edit`, and `mcp` permissions to allow full feature coordination and domain modeling capabilities.

### modes\methodologies\tdd-guide.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\roo-kilo\Roo-Todo-List.md

Create the Test Driven Development (TDD) Guide mode that implements TDD methodology with SDD integration. This mode will:

- Implement the Red-Green-Refactor cycle: write failing test, make it pass, refactor
- Focus on test-first development and automated testing
- Integrate with SDD workflow by:
  - Using `sdd-requirements-writer` for testable requirements with EARS acceptance criteria
  - Leveraging `sdd-design-architect` for test architecture and design patterns
  - Coordinating with `sdd-task-orchestrator` for test-driven task breakdown
- Provide guidance on:
  - Writing effective unit tests before implementation
  - Test case design from EARS requirements
  - Red-Green-Refactor cycle execution
  - Test automation and continuous integration
  - Mock objects and test doubles
  - Test coverage and quality metrics
  - Refactoring techniques and code quality
- Include templates for TDD practices adapted for SDD methodology:
  - Test case templates from EARS syntax
  - TDD workflow integration with SDD phases
  - Test automation and CI/CD integration
- Support test-first development practices
- Implement TDD best practices for different programming languages
- Focus on code quality and maintainability through testing
- Reference TDD principles and modern testing practices
- Include examples of TDD with EARS requirements translation

The mode will have `read`, `edit`, and `mcp` permissions to allow full test development and code quality management.

### modes\methodologies\bdd-facilitator.yaml(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\roo-kilo\Roo-Todo-List.md

Create the Behavior Driven Development (BDD) Facilitator mode that implements BDD methodology with SDD integration. This mode will:

- Implement the Given-When-Then syntax and Gherkin language for behavior specification
- Focus on collaboration between business, development, and QA teams (Three Amigos)
- Integrate with SDD workflow by:
  - Using `sdd-requirements-writer` for behavior scenarios with EARS and Gherkin syntax
  - Leveraging `sdd-design-architect` for behavior-driven architecture design
  - Coordinating with `sdd-task-orchestrator` for scenario-based task breakdown
- Provide guidance on:
  - Writing behavior scenarios in Gherkin syntax
  - Translating EARS requirements to Given-When-Then format
  - Facilitating Three Amigos sessions
  - Executable specifications and living documentation
  - Behavior-driven test automation
  - Acceptance criteria and definition of done
  - Stakeholder collaboration and shared understanding
- Include templates for BDD practices adapted for SDD methodology:
  - Gherkin scenario templates from EARS syntax
  - BDD workflow integration with SDD phases
  - Behavior specification and test automation
- Support behavior-driven development practices
- Implement BDD best practices for team collaboration
- Focus on shared understanding and executable specifications
- Reference BDD principles and Gherkin syntax standards
- Include examples of EARS to Gherkin translation
- Support tools like Cucumber and SpecFlow integration

The mode will have `read`, `edit`, and `mcp` permissions to allow full behavior specification and collaboration facilitation.

### modes\methodologies\.roomodes(NEW)

References: 

- .roomodes
- modes\firecrawl\.roomodes

Create the modular `.roomodes` configuration file for the methodologies modes. This file will:

- Follow the same YAML structure as the main `.roomodes` file and `modes/firecrawl/.roomodes`
- Include all six methodology modes in a single configuration
- Maintain consistency with the existing mode patterns
- Use the `customModes` array structure
- Include proper `source: project` designation for all modes
- Reference the individual YAML files for detailed mode definitions
- Provide a centralized configuration for the methodologies module
- Enable easy integration with the main configuration files
- Support the modular mode architecture established in the repository

The file will contain the six methodology modes:
1. `agile-coach` - Agile methodology principles and practices
2. `scrum-master` - Scrum framework implementation
3. `kanban-manager` - Kanban workflow management
4. `fdd-coordinator` - Feature Driven Development coordination
5. `tdd-guide` - Test Driven Development guidance
6. `bdd-facilitator` - Behavior Driven Development facilitation

Each mode will have appropriate permissions (`read`, `edit`, `mcp`) and follow the established patterns from existing modes. The configuration will ensure seamless integration with the SDD foundations and existing RooCode ecosystem.

### modes\methodologies\README.md(NEW)

References: 

- docs\SDD\Rapport-SDD-EARS.md
- docs\kiro\kiro.md
- docs\roo-kilo\Roo-Todo-List.md

Create comprehensive documentation for the Methodologies modes. This README will:

- Explain the six software development methodologies and their integration with SDD
- Document how each methodology mode complements the SDD foundations
- Provide usage examples for each methodology mode
- Explain the workflow integration between methodologies and SDD phases
- Include practical examples of methodology-specific practices
- Reference the theoretical foundations from the SDD documentation and Kiro methodology
- Provide best practices for using the methodology modes effectively
- Include troubleshooting and common usage patterns
- Document the integration with RooCode's existing features like todo lists and MCP tools
- Explain how the methodology modes complement existing orchestration capabilities
- Provide templates and examples for each methodology's artifacts
- Include guidelines for team adoption and methodology selection

The documentation will cover:
1. **Overview**: Software development methodologies and their benefits
2. **Agile Coach**: Agile principles and iterative development
3. **Scrum Master**: Scrum framework and ceremony facilitation
4. **Kanban Manager**: Flow-based workflow management
5. **FDD Coordinator**: Feature-driven development and domain modeling
6. **TDD Guide**: Test-driven development and quality assurance
7. **BDD Facilitator**: Behavior-driven development and collaboration
8. **Integration**: How methodologies work with SDD foundations
9. **Examples**: Complete workflow demonstrations
10. **Best Practices**: Tips for effective methodology adoption
11. **Troubleshooting**: Common issues and solutions
12. **Methodology Selection**: Guidance for choosing the right approach

---

