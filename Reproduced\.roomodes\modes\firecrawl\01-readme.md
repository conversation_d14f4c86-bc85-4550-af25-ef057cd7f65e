
# 🔥 Fire Crawler - Mode d’extraction de données et de web scraping pour Roo Code

Ce fichier README.md fournit une documentation complète pour le mode Fire Crawler, incluant la description des outils, l’explication des paramètres, les bonnes pratiques, des exemples d’utilisation, des considérations éthiques, des cas d’usage et des conseils de dépannage.

## Vue d’ensemble

Fire Crawler est un mode spécialisé qui exploite les puissantes capacités de crawling et d’extraction de données de Firecrawl. Ce mode Roo vous permet de collecter, analyser et structurer efficacement du contenu web pour divers usages : recherche, analyse concurrentielle, agrégation de contenu, collecte de données, etc.

## Fonctionnalités

- **Cartographie de site web** : Découverte et cartographie de la structure d’un site
- **Crawling récursif** : Exploration de sites avec profondeur et périmètre configurables
- **Extraction de données structurées** : Extraction de données spécifiques via des prompts en langage naturel
- **Scraping de contenu** : Récupération précise du contenu des pages web
- **Recherche web** : Recherche et récupération de contenus sur le web

## MCP.Composio.dev - Plateforme d’intégration Model Context Protocol

### Qu’est-ce que MCP.Composio.dev ?

MCP.Composio.dev est une plateforme serveur MCP (Model Context Protocol) gérée qui résout les principaux défis d’intégration des agents IA :

1. **Authentification & Autorisation** : Prise en charge intégrée de l’authentification pour plus de 300 applications, gérant automatiquement OAuth, clés API et authentification basique.
2. **Serveurs MCP préconfigurés** : Serveurs MCP prêts à l’emploi pour des services populaires comme GitHub, Slack, Linear, Google Workspace, etc.
3. **Intégration standardisée** : Conforme à la spécification MCP, compatible avec tout client conforme MCP.

## Fonctionnement

MCP.Composio.dev sert de pont entre les applications IA et les services externes :

1. **Connexion du client** : Les applications IA (ex : Cursor IDE, Claude Desktop) se connectent aux serveurs MCP Composio
2. **Gestion de l’authentification** : Composio gère tous les flux d’authentification de manière sécurisée
3. **Exposition des outils et ressources** : Composio expose des outils et ressources standardisés pour chaque service intégré
4. **Exécution & réponse** : Lorsqu’un outil est appelé, Composio gère les interactions API et retourne des réponses formatées

## Principaux avantages

- **Intégration sans configuration** : Connectez plus de 300 applications avec une configuration minimale
- **Gestion de l’authentification** : Plus besoin de gérer OAuth ou le stockage des clés API
- **Interface standardisée** : Modèle d’outils et de ressources cohérent sur toutes les intégrations
- **Support des frameworks** : Fonctionne avec OpenAI, Vercel AI SDK, LangChain, CrewAI, etc.

## Exemples de cas d’usage

- Connecter des agents IA à des outils de productivité (Slack, Gmail, Notion)
- Activer des workflows IA avec des outils de développement (GitHub, Linear, Jira)
- Créer des assistants IA avec accès aux systèmes d’entreprise
- Construire des workflows multi-services opérant sur différentes plateformes

MCP.Composio.dev élimine la complexité de la création et de la maintenance de serveurs MCP individuels, permettant aux développeurs de se concentrer sur l’expérience IA plutôt que sur l’intégration.

## Outils disponibles

### 1. FIRECRAWL_MAP_URLS

Cartographie la structure d’URL d’un site pour découvrir les pages disponibles.

```javascript
{
  "url": "https://example.com",       // URL de base pour démarrer la cartographie
  "limit": 10,                        // Nombre maximum d’URLs à retourner
  "includeSubdomains": false,         // Inclure ou non les sous-domaines
  "ignoreSitemap": false              // Ignorer ou non le sitemap
}
```

### 2. FIRECRAWL_CRAWL_URLS

Crawle les sites web récursivement avec des paramètres configurables.

```javascript
{
  "url": "https://example.com",                // URL de base pour démarrer le crawl
  "limit": 10,                                 // Nombre maximum de pages à crawler
  "maxDepth": 2,                               // Profondeur maximale du crawl
  "allowExternalLinks": false,                 // Suivre ou non les liens externes
  "allowBackwardLinks": true,                  // Suivre ou non les liens déjà visités
  "includePaths": ["blog/*"],                  // N’inclure que les chemins correspondant à ces motifs
  "excludePaths": ["admin/*"],                 // Exclure les chemins correspondant à ces motifs
  "scrapeOptions_onlyMainContent": true,       // Extraire uniquement le contenu principal
  "scrapeOptions_formats": ["markdown", "html"], // Formats de sortie
  "scrapeOptions_waitFor": 1000                // Temps d’attente (ms) avant extraction
}
```

### 3. FIRECRAWL_EXTRACT

Extrait des données structurées de pages web via des prompts en langage naturel ou des schémas JSON.

```javascript
{
  "urls": ["https://example.com/products/*"],  // URLs à extraire (wildcards supportés)
  "prompt": "Extraire les noms, prix et descriptions des produits", // Prompt en langage naturel
  "schema": {                                  // Alternative : schéma JSON
    "products": [{
      "name": "string",
      "price": "number",
      "description": "string"
    }]
  },
  "enable_web_search": false                   // Suivre ou non les liens externes
}
```

### 4. FIRECRAWL_SCRAPE_EXTRACT_DATA_LLM

Scrape un contenu spécifique de pages web avec options de formatage.

```javascript
{
  "url": "https://example.com",                // URL à scraper
  "onlyMainContent": true,                     // Extraire uniquement le contenu principal
  "formats": ["markdown"],                     // Formats de sortie
  "waitFor": 1000,                             // Temps d’attente (ms) avant extraction
  "actions": [                                 // Actions à effectuer avant scraping (optionnel)
    {"type": "click", "selector": ".button"}
  ]
}
```

### 5. FIRECRAWL_SEARCH

Effectue des recherches web et retourne les résultats pertinents.

```javascript
{
  "query": "meilleures pratiques web scraping", // Requête de recherche
  "limit": 5,                                  // Nombre maximum de résultats
  "country": "FR",                             // Code pays
  "lang": "fr",                                // Code langue
  "formats": ["markdown"]                      // Formats de sortie
}
```

### 6. FIRECRAWL_CRAWL_JOB_STATUS

Vérifie le statut d’un job de crawl.

```javascript
{
  "id": "job-uuid-here"                        // ID du job de crawl
}
```

### 7. FIRECRAWL_CANCEL_CRAWL_JOB

Annule un job de crawl en cours.

```javascript
{
  "id": "job-uuid-here"                        // ID du job de crawl
}
```

## Bonnes pratiques

1. **Commencer petit** : Lancez des crawls limités puis élargissez progressivement
2. **Utiliser des limites** : Définissez toujours des limites pour éviter un crawling excessif
3. **Cibler le crawl** : Utilisez includePaths/excludePaths pour viser le contenu pertinent
4. **Respecter les sites** : Ajoutez des délais entre les requêtes, n’inondez pas les serveurs
5. **Surveiller les jobs** : Vérifiez le statut des jobs pour les crawls longs
6. **Annuler si besoin** : Annulez les jobs inutiles pour économiser les ressources

## Exemples d’utilisation

### Cartographier la structure d’un site

```javascript
// Exemple de cartographie de structure de site
{
  "server_name": "firecrawl",
  "tool_name": "FIRECRAWL_MAP_URLS",
  "arguments": {
    "url": "https://example.com",
    "limit": 10
  }
}
```

### Extraire des informations produits

```javascript
// Exemple d’extraction d’informations produits
{
  "server_name": "firecrawl",
  "tool_name": "FIRECRAWL_EXTRACT",
  "arguments": {
    "urls": ["https://example.com/products/*"],
    "prompt": "Extraire les noms, prix et descriptions des produits"
  }
}
```

### Recherche d’informations

```javascript
// Exemple de recherche d’informations
{
  "server_name": "firecrawl",
  "tool_name": "FIRECRAWL_SEARCH",
  "arguments": {
    "query": "meilleures pratiques web scraping",
    "limit": 5
  }
}
```

## Considérations éthiques

Lorsque vous utilisez le mode Fire Crawler, gardez toujours à l’esprit ces principes :

1. **Respecter les conditions d’utilisation** : Consultez et respectez les CGU des sites visités
2. **Respecter robots.txt** : Suivez les directives des fichiers robots.txt
3. **Limiter la fréquence** : Ajoutez des délais pour ne pas surcharger les serveurs
4. **Respecter la vie privée** : Gérez les données personnelles de façon responsable et conforme
5. **Attribution** : Attribuez correctement le contenu à sa source lors de la republication
6. **Transparence** : Soyez clair sur vos activités de scraping et leur but

## Cas d’usage

- **Études de marché** : Collecte d’informations sur les prix et produits concurrents
- **Agrégation de contenu** : Récupération d’articles, blogs, etc.
- **Analyse de données** : Extraction de données structurées pour analyses
- **Génération de leads** : Collecte de contacts depuis des annuaires professionnels
- **Recherche académique** : Collecte de données pour projets de recherche
- **Analyse SEO** : Suivi des mots-clés et de la performance des contenus

## Dépannage

- **Limitation de débit** : Si vous obtenez des erreurs 429, augmentez les délais entre requêtes
- **Blocage d’accès** : Certains sites bloquent le scraping ; respectez leurs conditions
- **Erreurs de timeout** : Pour les gros crawls, fractionnez en lots et surveillez le statut
- **Qualité des données** : Vérifiez l’exactitude et la complétude des données extraites
- **Problèmes de format** : Essayez différents formats de sortie si le contenu n’est pas bien extrait

